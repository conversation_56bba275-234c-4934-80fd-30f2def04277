// import React from 'react';
// import EmailItem from './EmailItem';

// const emails = [
//   {
//     id: 1,
//     read: true,
//     starred: true,
//     sender: '<PERSON>',
//     subject: 'Meeting Tomorrow',
//     preview: 'Hi there, just a reminder about our meeting tomorrow at 10 AM...',
//     timestamp: '10:30 AM',
//   },
//   {
//     id: 2,
//     read: false,
//     starred: false,
//     sender: 'Amazon',
//     subject: 'Your Amazon order has shipped',
//     preview: 'Your recent order #12345 has been shipped and will arrive...',
//     timestamp: 'Yesterday',
//   },
//   {
//     id: 3,
//     read: false,
//     starred: false,
//     sender: 'LinkedIn',
//     subject: 'You have 5 new connection requests',
//     preview: 'You have 5 new connection requests waiting for your response...',
//     timestamp: 'Mar 15',
//   },
//   {
//     id: 4,
//     read: false,
//     starred: true,
//     sender: '<PERSON>',
//     subject: 'Project Update: Q2 Goals',
//     preview: 'Here\'s the latest update on our Q2 goals and objectives...',
//     timestamp: 'Mar 14',
//   },
//   {
//     id: 5,
//     read: false,
//     starred: false,
//     sender: 'Netflix',
//     subject: 'New shows added to your list',
//     preview: 'Check out the new shows we\'ve added based on your preferences...',
//     timestamp: 'Mar 12',
//   },
// ];

// const EmailList = () => {
//   return (
//     <div className="flex-1 overflow-y-auto\">
//       {emails.map((email) => (
//         <EmailItem key={email.id} email={email} />
//       ))}
//     </div>
//   );
// };

// export default EmailList;




import React, { useEffect } from 'react';
import EmailItem from './EmailItem';
import { Email } from '../../types/email';

interface EmailListProps {
  emails: Email[];
  selectedEmails: Set<number>;
  onEmailSelect: (emailId: number) => void;
  onToggleStar: (emailId: number) => void;
  onEmailClick: (email: Email) => void;
  searchQuery: string;
}

const EmailList: React.FC<EmailListProps> = ({
  emails,
  selectedEmails,
  onEmailSelect,
  onToggleStar,
  onEmailClick,
  searchQuery,
}) => {

  // Debug: Log what emails are being received
  console.log('📧 EmailList received emails:', emails.length);
  console.log('📧 EmailList emails:', emails);
  console.log('📧 EmailList searchQuery:', searchQuery);

  // Monitor when emails prop changes
  useEffect(() => {
    console.log('📧 EmailList: emails prop CHANGED!');
    console.log('📧 New emails count:', emails.length);
    console.log('📧 Search query:', searchQuery);
  }, [emails, searchQuery]);

  if (emails.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery ? 'No emails found' : 'No emails in this folder'}
          </h3>
          <p className="text-gray-500">
            {searchQuery 
              ? `No emails match "${searchQuery}"`
              : 'This folder is empty'
            }
          </p>
        </div>
      </div>
    );
  }


  return (
    <div className="flex-1 overflow-y-auto">
      {emails.map((email) => (
        <EmailItem
          key={email.id}
          email={email}
          isSelected={selectedEmails.has(email.id)}
          onSelect={onEmailSelect}
          onToggleStar={onToggleStar}
          onClick={onEmailClick}
        />
      ))}
    </div>
  );
};

export default EmailList;