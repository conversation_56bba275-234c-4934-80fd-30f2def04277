import { useEffect } from 'react';
import { useEmailStore } from '../stores/emailStore';
import { usePagination } from '../pages/report_outage/hooks/usePagination';

/**
 * Custom hook that combines Zustand email store with pagination
 * This replaces the old useEmailState hook
 */
export const useEmailWithPagination = () => {
  // Get all state and actions from Zustand store
  const {
    emails,
    selectedEmails,
    currentView,
    searchQuery,
    selectedEmail,
    showCompose,
    loading,
    appliedFilters,
    // Actions
    setEmails,
    setLoading,
    setSearchQuery,
    setFilters,
    clearFilters,
    toggleEmailSelection,
    selectAllEmails,
    toggleStar,
    markAsRead,
    markAsUnread,
    deleteEmails,
    archiveEmails,
    setCurrentView,
    selectEmail,
    setShowCompose,
    updateEmailFields,
    fetchEmails,
    refresh,
  } = useEmailStore();

  // Set up pagination on the emails from Zustand store
  const pagination = usePagination(emails, {
    itemsPerPage: 25,
    maxVisiblePages: 7,
    showFirstLast: true,
    showPrevNext: true,
  });

  // Fetch emails on component mount
  useEffect(() => {
    fetchEmails();
  }, [fetchEmails]);

  // Return the same interface as the old useEmailState hook
  return {
    state: {
      emails,
      selectedEmails,
      currentView,
      searchQuery,
      selectedEmail,
      showCompose,
      appliedFilters,
    },
    loading,
    pagination,
    actions: {
      toggleEmailSelection,
      selectAllEmails,
      toggleStar,
      markAsRead,
      markAsUnread,
      deleteEmails,
      archiveEmails,
      setCurrentView,
      setSearchQuery,
      setFilters,
      clearFilters,
      selectEmail,
      setShowCompose,
      updateEmailFields,
      refresh,
    },
  };
};
