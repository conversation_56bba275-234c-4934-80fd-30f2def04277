import apiClient from "../apiClient";

export interface BasestationReq {
	station_code: string;
	region: string;
	csc: string;
	substation: string;
	feeder: string;
	address: string;
	gps_location: string;
}

export interface InspectionReq {
	body_condition: string; // Condition of the transformer body
	arrester: string; // Details about arresters
	drop_out: string; // Details about drop-out mechanisms
	fuse_link: string; // Details about fuse links
	bushing: string; // Details about bushings
	cable_lugs: string; // Details about cable lugs
	horn_gap: string; // Details about horn gaps
	tap_changer_position: string; // Position of the tap changer
	oil_level: string; // Oil level in the transformer
	oil_leakage: string; // Details about oil leakage
	silica_gel: string; // Condition of silica gel
	cable_size: string; // Size of the cables
	neutral_ground: string; // Details about neutral grounding
	arrester_body_ground: string;
	N_load_current: string; // Neutral load current (numeric value)
	R_S_Voltage: string; // Voltage between R and S phases (numeric value)
	R_T_Voltage: string; // Voltage between R and T phases (numeric value)
	T_S_Voltage: string; // Voltage between T and S phases (numeric value)
	transformer_data_id?: number | null; // Optional ID of the associated transformer data
}

export interface LvFeederReq {
	distribution_box_name: string; // Corresponds to CharField(max_length=125)
	inspection_id: number; // Corresponds to BigIntegerField()
	R_load_current: number; // Corresponds to IntegerField()
	S_load_current: number; // Corresponds to IntegerField()
	T_load_current: number; // Corresponds to IntegerField()
	R_fuse_rating: string; // Corresponds to CharField(max_length=125)
	S_fuse_rating: string; // Corresponds to CharField(max_length=125)
	T_fuse_rating: string; // Corresponds to CharField(max_length=125)
	inspection_data: number | null;

	// inspection_data: InspectionData; // Nested relationship for Inspection
}

export type BasestationRes = {
	results: never[];
	Basestation: Array<any>;
};

export type InspectionRes = {
	results: never[];
	Inspection: Array<any>;
};

export enum TransformerApi {
	createBasestation = "/api/transformer/basestations/",
	getBasestations = "/api/transformer/basestations/",
	updateBasestation = "/api/transformer/basestations/:station_code/", // Include :id for updates
	deleteBasestation = "/api/transformer/basestations/:station_code/",
	getBasestation = "/api/transformer/basestations/:station_code/", // Add this line

	createTransformer = "/api/transformer/transformerdata/",
	getTransformer = "/api/transformer/transformerdata/",
	updateTransformer = "/api/transformer/transformerdata/:id/", // Include :id for updates
	deleteTransformer = "/api/transformer/transformerdata/:id/",
	getPopulatedTransformer = "/api/transformer/populatedtransformerdata/:id",

	createInspection = "/api/transformer/inspections/",
	getInspections = "/api/transformer/inspections/",
	updateInspection = "/api/transformer/inspections/:station_code/", // Include :id for updates
	deleteInspection = "/api/transformer/inspections/:station_code/",
	getPopulatedInspection = "/api/transformer/populatedInspection/:id",

	createLvFeeder = "/api/transformer/lvfeeders/",
	getLvFeeders = "/api/transformer/lvfeeders/",
	updateLvFeeder = "/api/transformer/lvfeeders/:id/", // Include :id for updates
	deleteLvFeeder = "/api/transformer/lvfeeders/:id/",
	getPopulatedLvFeeder = "/api/transformer/populatedlvfeeder/:id",

	getBasestationsFiltered = "api/transformer/basestationsFiltered/",
	getBasestationTransformerHistory = "/api/transformer/basestations/:station_code/transformer-history/",
}

const createBasestation = (data: BasestationReq) =>
	apiClient.post<BasestationRes>({ url: TransformerApi.createBasestation, data });

const getBasestations = async (params: { page?: number } = {}) =>
	apiClient.get<any>({ url: TransformerApi.getBasestations, params });

const updateBasestation = async (station_code: string, data: Partial<BasestationReq>): Promise<void> => {
	try {
		await apiClient.patch<any>({
			url: TransformerApi.updateBasestation.replace(":station_code", station_code), // Replace :id with actual ID
			data, // Pass the updated data payload
		});
	} catch (error) {
		throw new Error("Failed to update basestation");
	}
};

const deleteBasestation = async (station_code: string): Promise<void> => {
	try {
		await apiClient.delete<any>({
			url: TransformerApi.deleteBasestation.replace(":station_code", station_code), // Replace :id with actual ID
		});
	} catch (error) {
		throw new Error("Failed nnn to delete basestation");
	}
};

const createTransformer = async (data: any) => {
	try {
		const response = await apiClient.post<any>({ 
			url: TransformerApi.createTransformer, 
			data 
		});
		return response; // Return the created transformer data
	} catch (error) {
		console.log("Error creating Transformer:", error.response?.data);
		if (error.response?.data?.basestation?.[0]?.includes("object does not exist")) {
			return { error: "Base Station does not exist" };
		}
		else if (error.response?.data?.basestation?.[0]?.includes("transformer data with this basestation already exists.")) {
			return { error: "Transformer with this Base Station already exists." };
		}
		else if (error.response?.data?.serial_number?.[0]?.includes("with this serial number already exists.")) {
			return { error: "Transformer with this Serial Number already exists." };
		}
		else if (error.response?.data) {
			return { error: JSON.stringify(error.response?.data) };
		}
		throw new Error("Failed to update Transformer");
	}
};

const getTransformer = async (params: { page?: number } = {}) =>
	apiClient.get<any>({ url: TransformerApi.getTransformer, params });

const getPopulatedTransformer = async (id: string) =>
	apiClient.get<any>({ url: TransformerApi.getPopulatedTransformer.replace(":id", id) });

const updateTransformer = async (id: number, data: Partial<any>): Promise<any> => {
	try {
		const response = await apiClient.put<any>({
			url: TransformerApi.updateTransformer.replace(":id", id.toString()),
			data,
		});
		return response; // Return the updated transformer data
	} catch (error) {
		console.log("Error creating Transformer:", error.response?.data);
		if (error.response?.data?.basestation?.[0]?.includes("object does not exist")) {
			return { error: "Base Station does not exist" };
		}
		else if (error.response?.data?.basestation?.[0]?.includes("transformer data with this basestation already exists.")) {
			return { error: "Transformer with this Base Station already exists." };
		}
		else if (error.response?.data?.serial_number?.[0]?.includes("transformer data with this Serial Number already exists.")) {
			return { error: "Transformer with this Serial Number already exists." };
		}
		else if (error.response?.data) {
			return { error: JSON.stringify(error.response?.data) };
		}
		throw new Error("Failed to update Transformer");
	}
};

const deleteTransformer = async (id: number): Promise<void> => {
	try {
		await apiClient.delete<any>({
			url: TransformerApi.deleteTransformer.replace(":id", id.toString()), // Replace :id with actual ID
		});
	} catch (error) {
		throw new Error("Failed nnn to delete Transformer");
	}
};

const createInspection = async (data: InspectionReq) => {
	const response = await apiClient.post<InspectionRes>({ 
		url: TransformerApi.createInspection, 
		data 
	});
	return response;
};

const getInspections = async (params: { page?: number; transformer_data?: number } = {}) =>
	apiClient.get<any>({ url: TransformerApi.getInspections, params });

const updateInspection = async (id: string, data: Partial<InspectionReq>): Promise<any> => {
	try {
		const response = await apiClient.patch<any>({
			url: TransformerApi.updateInspection.replace(":station_code", id.toString()),
			data,
		});
		return response;
	} catch (error) {
		throw new Error("Failed to update inspection");
	}
};

const deleteInspection = async (station_code: string): Promise<void> => {
	try {
		await apiClient.delete<any>({
			url: TransformerApi.deleteInspection.replace(":station_code", station_code), // Replace :id with actual ID
		});
	} catch (error) {
		throw new Error("Failed nnn to delete inspection");
	}
};

const getPopulatedInspection = async (id: string) =>
	apiClient.get<any>({ url: TransformerApi.getPopulatedInspection.replace(":id", id) });

const createLvFeeder = async (data: LvFeederReq): Promise<LvFeederRes> => {
	try {
		const response = await apiClient.post<LvFeederRes>({ 
			url: TransformerApi.createLvFeeder, 
			data 
		});
		return response;
	} catch (error: any) {
		const errorMessage = error.response?.data?.message || "Failed to create LvFeeder";
		throw new Error(errorMessage);
	}
};

const getLvFeeders = async (params: { page?: number; inspection_data?: number } = {}) =>
	apiClient.get<any>({ url: TransformerApi.getLvFeeders, params });

const updateLvFeeder = async (id: string, data: Partial<LvFeederReq>): Promise<any> => {
	try {
		const response = await apiClient.patch<any>({
			url: TransformerApi.updateLvFeeder.replace(":id", id),
			data,
		});
		return response;
	} catch (error: any) {
		const errorMessage = error.response?.data?.message || "Failed to update LvFeeder";
		throw new Error(errorMessage);
	}
};

const deleteLvFeeder = async (id: string): Promise<void> => {
	try {
		await apiClient.delete<any>({
			url: TransformerApi.deleteLvFeeder.replace(":id", id), // Replace :id with actual ID
		});
	} catch (error) {
		throw new Error("Failed nnn to delete inspection");
	}
};

const getPopulatedLvFeeder = async (id: string) =>
	apiClient.get<any>({ url: TransformerApi.getPopulatedLvFeeder.replace(":id", id) });

const getBasestationsFiltered = async (params: any) => {
	const response = await apiClient.get<any>({ url: TransformerApi.getBasestationsFiltered, params });
	return response;
};

const getBasestation = async (station_code: string) => {
	try {
		const response = await apiClient.get<Basestation>({ 
			url: TransformerApi.getBasestation.replace(":station_code", station_code)
		});
		return response;
	} catch (error) {
		throw new Error("Failed to fetch basestation details");
	}
};

const getBasestationChangeLogs = async (station_code: string, page: number, pageSize: number) => {
	try {
		const response = await apiClient.get({
			url: `${TransformerApi.getBasestation.replace(":station_code", station_code)}changes`,
			params: {
				page,
				page_size: pageSize
			}
		});
		return response;
	} catch (error) {
		throw new Error("Failed to fetch basestation change logs");
	}
};

const getBasestationTransformerHistory = async (station_code: string, page: number = 1) => {
	try {
		const response = await apiClient.get({
			url: TransformerApi.getBasestationTransformerHistory.replace(":station_code", station_code),
			params: {
				page,
				page_size: 10
			}
		});
		return response;
	} catch (error) {
		throw new Error("Failed to fetch transformer history");
	}
};

export default {
	createBasestation,
	getBasestations,
	updateBasestation,
	deleteBasestation,
	getBasestation,
	getBasestationChangeLogs,
	getBasestationTransformerHistory,

	createTransformer,
	getTransformer,
	updateTransformer,
	deleteTransformer,
	getPopulatedTransformer,

	createInspection,
	getInspections,
	updateInspection,
	deleteInspection,
	getPopulatedInspection,

	createLvFeeder,
	getLvFeeders,
	updateLvFeeder,
	deleteLvFeeder,
	getPopulatedLvFeeder,

	getBasestationsFiltered,
};












