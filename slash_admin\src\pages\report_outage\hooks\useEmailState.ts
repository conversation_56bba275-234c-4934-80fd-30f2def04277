import { useState, useCallback, useMemo, useEffect } from 'react';
import { Email, EmailState } from '../../../types/email';
import { usePagination } from './usePagination';
import reportService from '@/api/services/reportService';

/**
 * Custom hook for managing email state and operations
 *
 * Provides comprehensive email management functionality including:
 * - Email fetching and caching
 * - Search and filtering
 * - Selection management
 * - CRUD operations (create, read, update, delete)
 * - Pagination
 * - Loading states
 *
 * @returns Object containing state, loading status, filtered emails, pagination, and actions
 */
export const useEmailState = () => {
  const [state, setState] = useState<EmailState>({
    emails: [],
    selectedEmails: new Set(),
    currentView: 'inbox',
    searchQuery: '',
    selectedEmail: null,
    showCompose: false,
  });

  const [loading, setLoading] = useState<boolean>(false);

  // Fetch emails from API
  const fetchEmails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await reportService.getReports();
      console.log('API Response:', response);
      setState(prev => ({
        ...prev,
        emails: response.results || response, // adapt to your API response shape
      }));
    } catch (error) {
      console.error('Failed to fetch emails:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEmails();
  }, [fetchEmails]);

  // Filter emails based on current view and search
  const filteredEmails = useMemo(() => {
    let emails = state.emails;

    // Filter by view
    switch (state.currentView) {
      case 'starred':
        emails = emails.filter(email => email.starred);
        break;
      case 'sent':
        // For sent emails, we could filter by some criteria or show empty for now
        emails = []; // No sent emails in current data structure
        break;
      case 'drafts':
        emails = []; // No drafts in current data structure
        break;
      case 'trash':
        emails = []; // No trash in current data structure
        break;
      case 'inbox':
      default:
        // Show all emails for inbox
        break;
    }

    // Filter by search query
    if (state.searchQuery) {
      const query = state.searchQuery.toLowerCase();
      emails = emails.filter(email =>
        email.sender.toLowerCase().includes(query) ||
        email.phone.toLowerCase().includes(query) ||
        email.address.toLowerCase().includes(query) ||
        email.canumber.toLowerCase().includes(query) ||
        email.complaint.toLowerCase().includes(query) ||
        email.content.toLowerCase().includes(query)
      );
    }

    return emails;
  }, [state.emails, state.currentView, state.searchQuery]);

  // Pagination
  const pagination = usePagination(filteredEmails, {
    itemsPerPage: 25,
    maxVisiblePages: 7,
    showFirstLast: true,
    showPrevNext: true,
  });

  const toggleEmailSelection = useCallback((emailId: number) => {
    setState(prev => {
      const newSelected = new Set(prev.selectedEmails);
      if (newSelected.has(emailId)) {
        newSelected.delete(emailId);
      } else {
        newSelected.add(emailId);
      }
      return { ...prev, selectedEmails: newSelected };
    });
  }, []);

  const selectAllEmails = useCallback((selectAll: boolean) => {
    setState(prev => ({
      ...prev,
      selectedEmails: selectAll ? new Set(pagination.currentItems.map(e => e.id)) : new Set(),
    }));
  }, [pagination.currentItems]);

  const toggleStar = useCallback(async (emailId: number) => {
  setState(prev => {
    const updatedEmails = prev.emails.map(email =>
      email.id === emailId ? { ...email, starred: !email.starred } : email
    );

    let updatedSelectedEmail = prev.selectedEmail;
    if (prev.selectedEmail && prev.selectedEmail.id === emailId) {
      const found = updatedEmails.find(e => e.id === emailId);
      if (found) updatedSelectedEmail = found;
    }

    return {
      ...prev,
      emails: updatedEmails,
      selectedEmail: updatedSelectedEmail,
    };
  });

  const email = state.emails.find(e => e.id === emailId);

  if (email) {
    try {
      await reportService.updateReport(emailId, { starred: !email.starred });
      console.log('Star status updated successfully');
    } catch (error) {
      console.error('Failed to update star status:', error);
      // Optionally: revert local state if API call fails
    }
  }
}, [state.emails]);

const markAsRead = useCallback(async (emailId: number, read: boolean = true) => {
  setState(prev => ({
    ...prev,
    emails: prev.emails.map(email =>
      email.id === emailId ? { ...email, read } : email
    ),
  }));

  try {
    await reportService.updateReport(emailId, { read });
    // Optionally refetch or handle success
  } catch (error) {
    console.error('Failed to update read status:', error);
    // Optionally revert local state or show error
  }
}, []);

const deleteEmails = useCallback(async (emailIds: number[]) => {
  setState(prev => ({
    ...prev,
    emails: prev.emails.filter(email => !emailIds.includes(email.id)),
    selectedEmails: new Set(),
  }));

  try {
    await Promise.all(emailIds.map(id => reportService.deleteReport(id)));
    // Optionally refetch or handle success
  } catch (error) {
    console.error('Failed to delete emails:', error);
    // Optionally revert local state or show error
  }
}, []);

  const archiveEmails = useCallback((emailIds: number[]) => {
    setState(prev => ({
      ...prev,
      emails: prev.emails.filter(email => !emailIds.includes(email.id)),
      selectedEmails: new Set(),
    }));
  }, []);

  const setCurrentView = useCallback((view: EmailState['currentView']) => {
    setState(prev => ({ ...prev, currentView: view, selectedEmails: new Set() }));
  }, []);

  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
  }, []);

  const selectEmail = useCallback((email: Email | null) => {
    setState(prev => ({ ...prev, selectedEmail: email }));
    if (email && !email.read) {
      markAsRead(email.id);
    }
  }, [markAsRead]);

  const setShowCompose = useCallback((show: boolean) => {
    setState(prev => ({ ...prev, showCompose: show }));
  }, []);

  const updateEmailFields = useCallback(async (id: number, fields: Partial<Email>) => {
    try {
      await reportService.updateReport(id, fields);
      console.log('Email fields updated successfully');
    } catch (error) {
      console.error('Failed to update email fields:', error);
      // Optionally: revert local state or show error
      return;
    }

    setState(prev => ({
      ...prev,
      emails: prev.emails.map(email =>
        email.id === id ? { ...email, ...fields } : email
      ),
      selectedEmail:
        prev.selectedEmail && prev.selectedEmail.id === id
          ? { ...prev.selectedEmail, ...fields }
          : prev.selectedEmail,
    }));
  }, []);

  // Refresh action
  const refresh = useCallback(() => {
    fetchEmails();
  }, [fetchEmails]);

  return {
    state: {
      ...state,
      emails: pagination.currentItems, // Use paginated emails
    },
    loading,
    filteredEmails,
    pagination,
    actions: {
      toggleEmailSelection,
      selectAllEmails,
      toggleStar,
      markAsRead,
      deleteEmails,
      archiveEmails,
      setCurrentView,
      setSearchQuery,
      selectEmail,
      setShowCompose,
      updateEmailFields,
      refresh,
    },
  };
};