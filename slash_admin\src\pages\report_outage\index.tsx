import { useEffect } from 'react'
import EmailToolbar from './EmailToolbar'
import PaginationControls from './PaginationControls'
import EmailList from './EmailList'
import { useEmailWithPagination } from '../../hooks/useEmailWithPagination';
import EmailViewer from './EmailViewer';

/**
 * Report Outage Page Component
 *
 * Main page for managing power outage reports. Provides functionality to:
 * - View and search through email reports
 * - Select and perform bulk actions on emails
 * - View individual email details
 * - Paginate through large sets of emails
 */
function ReportOutagePage() {

  const { state, loading, pagination, actions } = useEmailWithPagination();

  const selectedEmailIds = Array.from(state.selectedEmails);
  const allSelected = state.selectedEmails.size === state.emails.length && state.emails.length > 0;

  const handleMarkAsRead = () => {
    actions.markAsRead(selectedEmailIds);
  };

  const handleMarkAsUnread = () => {
    actions.markAsUnread(selectedEmailIds);
  };

  const handleDelete = () => {
    actions.deleteEmails(selectedEmailIds);
  };

  const handleArchive = () => {
    actions.archiveEmails(selectedEmailIds);
  };

  const handleRefresh = () => {
    actions.refresh();
    console.log('Refreshing emails...');
  };

  const handleSaveAll = (id: number, requestNumber: string, remark: string, status: string) => {
    console.log('Saving all fields...', id, requestNumber, remark, status);
    actions.updateEmailFields(id, { requestNumber, remark, status });
  };

  console.log('=== INDEX.TSX DEBUG (ZUSTAND) ===');
  console.log('Loading:', loading);
  console.log('Search Query:', state.searchQuery);
  console.log('Total Server-Filtered Emails:', state.emails.length);
  console.log('Server-Filtered Emails:', state.emails);
  console.log('Paginated Items Count:', pagination.currentItems.length);
  console.log('Paginated Items (displayed):', pagination.currentItems);
  console.log('Pagination Info:', {
    currentPage: pagination.currentPage,
    totalPages: pagination.totalPages,
    totalItems: pagination.totalItems
  });
  console.log('===================================');

  // Monitor when emails actually change (Zustand)
  useEffect(() => {
    console.log('🎯 INDEX.TSX (ZUSTAND): state.emails CHANGED!');
    console.log('🎯 New email count:', state.emails.length);
    console.log('🎯 Current search query:', state.searchQuery);
    console.log('🎯 Loading state:', loading);
  }, [state.emails, state.searchQuery, loading]);


  return (
    <main className="flex-1 flex flex-col overflow-hidden bg-white">
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
      {/* Main Content */}
        {state.selectedEmail ? (
          <EmailViewer
            email={state.selectedEmail}
            onBack={() => actions.selectEmail(null)}
            onToggleStar={actions.toggleStar}
            onArchive={(id) => actions.archiveEmails([id])}
            onDelete={(id) => actions.deleteEmails([id])}
            onSaveAll={handleSaveAll}
          />
        ) : (
          <main className="flex-1 flex flex-col overflow-hidden bg-white">
            {/* Email Toolbar */}
            <EmailToolbar
              selectedCount={state.selectedEmails.size}
              totalCount={pagination.totalItems}
              onSelectAll={actions.selectAllEmails}
              onRefresh={handleRefresh}
              onArchive={handleArchive}
              onDelete={handleDelete}
              onMarkAsRead={handleMarkAsRead}
              onMarkAsUnread={handleMarkAsUnread}
              allSelected={allSelected}
            />

            {/* Email List */}
            <EmailList
              emails={pagination.currentItems}
              selectedEmails={state.selectedEmails}
              onEmailSelect={actions.toggleEmailSelection}
              onToggleStar={actions.toggleStar}
              onEmailClick={actions.selectEmail}
              searchQuery={state.searchQuery}
            />

            {/* Debug: Show what we're passing to EmailList */}
            <div style={{ display: 'none' }}>
              Passing to EmailList: {pagination.currentItems.length} emails
            </div>

            {/* Pagination */}
            <div className="border-t border-gray-200 px-6 py-3 bg-white">
              <PaginationControls
                currentPage={pagination.currentPage}
                totalPages={pagination.totalPages}
                totalEmails={pagination.totalItems}
                emailsPerPage={pagination.itemsPerPage}
                startIndex={pagination.startIndex}
                endIndex={pagination.endIndex}
                hasNextPage={pagination.hasNextPage}
                hasPrevPage={pagination.hasPrevPage}
                visiblePages={pagination.visiblePages}
                onPageChange={pagination.goToPage}
                onNextPage={pagination.goToNextPage}
                onPrevPage={pagination.goToPrevPage}
                onFirstPage={pagination.goToFirstPage}
                onLastPage={pagination.goToLastPage}
                onItemsPerPageChange={pagination.setItemsPerPage}
              />
            </div>
          </main>
        )}
      </div>
    </main>
  )
}

export default ReportOutagePage