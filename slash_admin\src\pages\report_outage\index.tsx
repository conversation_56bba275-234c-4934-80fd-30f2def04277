import React, { useEffect } from 'react'
import Em<PERSON><PERSON><PERSON>bar from './EmailToolbar'
import PaginationControls from './PaginationControls'
import EmailList from './EmailList'
import { useEmailState } from './hooks/useEmailState';
import <PERSON>ail<PERSON>iewer from './EmailViewer';

function index() {

  const { state, pagination, actions } = useEmailState();

  const unreadCount = state.emails.filter(email => !email.read).length;
  const selectedEmailIds = Array.from(state.selectedEmails);
  const allSelected = state.selectedEmails.size === state.emails.length && state.emails.length > 0;

  const handleMarkAsRead = () => {
    selectedEmailIds.forEach(id => actions.markAsRead(id, true));
  };

  const handleMarkAsUnread = () => {
    selectedEmailIds.forEach(id => actions.markAsRead(id, false));
  };

  const handleDelete = () => {
    actions.deleteEmails(selectedEmailIds);
  };

  const handleArchive = () => {
    actions.archiveEmails(selectedEmailIds);
  };

  const handleRefresh = () => {
     actions.refresh();
    // Simulate refresh
    console.log('Refreshing emails...');
  };

  const handleSaveAll = (id: any, requestNumber: any, remark: any, status: any) => {
    console.log('Saving all fields...', id, requestNumber, remark, status);
    actions.updateEmailFields(id, { requestNumber, remark, status });
    // If you want to call an API, do it here as well
    // await api.updateEmail(id, { requestNumber, remark, status });
  };

  useEffect(() => {
    console.log('state.emails______________________________', state.emails);
    console.log('state.searchedEmails______________________', state.searchedEmails);
  }, [state.emails, state.searchedEmails]);

 console.log('state.emails______________________________', state.emails);
  console.log('state.searchedEmails______________________', state.searchedEmails);

  return (
    <main className="flex-1 flex flex-col overflow-hidden bg-white">
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
      {/* Main Content */}
        {state.selectedEmail ? (
          <EmailViewer
            email={state.selectedEmail}
            onBack={() => actions.selectEmail(null)}
            onToggleStar={actions.toggleStar}
            onArchive={(id) => actions.archiveEmails([id])}
            onDelete={(id) => actions.deleteEmails([id])}
            onSaveAll={handleSaveAll}
          />
        ) : (
          <main className="flex-1 flex flex-col overflow-hidden bg-white">
            {/* Email Toolbar */}
            <EmailToolbar
              selectedCount={state.selectedEmails.size}
              totalCount={pagination.totalItems}
              onSelectAll={actions.selectAllEmails}
              onRefresh={handleRefresh}
              onArchive={handleArchive}
              onDelete={handleDelete}
              onMarkAsRead={handleMarkAsRead}
              onMarkAsUnread={handleMarkAsUnread}
              allSelected={allSelected}
            />

            {/* Email List */}
            <EmailList
              emails={state.emails}
              selectedEmails={state.selectedEmails}
              onEmailSelect={actions.toggleEmailSelection}
              onToggleStar={actions.toggleStar}
              onEmailClick={actions.selectEmail}
              searchQuery={state.searchQuery}
              // filteredEmails={filteredEmails}
            />

            {/* Pagination */}
            <div className="border-t border-gray-200 px-6 py-3 bg-white">
              {/* <PaginationControls
                currentPage={1}
                totalPages={1}
                totalEmails={state.emails.length}
                emailsPerPage={state.emails.length}
                onPageChange={() => {}}
              /> */}
               <PaginationControls
                currentPage={pagination.currentPage}
                totalPages={pagination.totalPages}
                totalEmails={pagination.totalItems}
                emailsPerPage={pagination.itemsPerPage}
                startIndex={pagination.startIndex}
                endIndex={pagination.endIndex}
                hasNextPage={pagination.hasNextPage}
                hasPrevPage={pagination.hasPrevPage}
                visiblePages={pagination.visiblePages}
                onPageChange={pagination.goToPage}
                onNextPage={pagination.goToNextPage}
                onPrevPage={pagination.goToPrevPage}
                onFirstPage={pagination.goToFirstPage}
                onLastPage={pagination.goToLastPage}
                onItemsPerPageChange={pagination.setItemsPerPage}
              />
            </div>
          </main>
        )}
      </div>
    </main>
  )
}

export default index