// // import React from 'react';
// // import { FaStar, FaRegStar } from 'react-icons/fa';

// // const EmailItem = ({ email }) => {
// //   const { id, read, starred, sender, subject, preview, timestamp } = email;

// //   return (
// //     <div
// //       className={`email-item flex items-center px-4 py-2 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
// //         read ? 'bg-blue-50' : ''
// //       }`}
// //       data-id={id}
// //       data-read={read}
// //     >
// //       <div className="flex items-center w-48">
// //         <input type="checkbox" className="email-checkbox rounded text-blue-500 mr-4" />
// //         <i
// //           className={`star-toggle ${starred ? 'fas fa-star text-yellow-400' : 'far fa-star text-gray-400'} mr-3`}
// //           data-starred={starred}
// //         ></i>
// //         <span className="font-medium truncate">{sender}</span>
// //       </div>
// //       <div className="flex-1 truncate">
// //         <span className="font-medium">{subject}</span>
// //         <span className="text-gray-500 ml-2">{preview}</span>
// //       </div>
// //       <div className="ml-4 text-sm text-gray-500 whitespace-nowrap">{timestamp}</div>
// //     </div>
// //   );
// // };

// // export default EmailItem;



// import React from 'react';
// import { Star, Paperclip } from 'lucide-react';
// import { Email } from '../../types/email';

// interface EmailItemProps {
//   email: Email;
//   isSelected: boolean;
//   onSelect: (emailId: number) => void;
//   onToggleStar: (emailId: number) => void;
//   onClick: (email: Email) => void;
// }

// const EmailItem: React.FC<EmailItemProps> = ({
//   email,
//   isSelected,
//   onSelect,
//   onToggleStar,
//   onClick,
// }) => {
//   const handleStarClick = (e: React.MouseEvent) => {
//     e.stopPropagation();
//     onToggleStar(email.id);
//   };

//   const handleCheckboxClick = (e: React.ChangeEvent<HTMLInputElement>) => {
//     e.stopPropagation();
//     onSelect(email.id);
//   };

//   return (
//     <div
//       className={`flex items-center px-4 py-3 border-b border-gray-100 hover:shadow-sm cursor-pointer transition-all duration-150 ${
//         !email.read ? 'bg-white font-medium' : 'bg-gray-50'
//       } ${isSelected ? 'bg-blue-50 border-blue-200' : ''}`}
//       onClick={() => onClick(email)}
//     >
//       {/* Checkbox */}
//       <div className="flex items-center mr-4">
//         <input
//           type="checkbox"
//           checked={isSelected}
//           onChange={handleCheckboxClick}
//           className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
//         />
//       </div>

//       {/* Star */}
//       <button
//         onClick={handleStarClick}
//         className="mr-4 p-1 hover:bg-gray-200 rounded transition-colors"
//       >
       
//         <Star
//           size={16}
//           color={email.starred ? '#facc15' : '#9ca3af'} // yellow-400 or gray-400
//           fill={email.starred ? '#facc15' : 'none'}
//           className="transition-colors"
//         />
//       </button>

//       {/* Important marker */}
//       {email.important && (
//         <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
//       )}

//       {/* Sender */}
//       <div className="w-48 truncate mr-4">
//         <span className={`${!email.read ? 'font-semibold' : 'font-normal'} text-gray-900`}>
//           {email.sender}
//         </span>
//       </div>

//       {/* Subject and Preview */}
//       <div className="flex-1 min-w-0 mr-4">
//         <div className="truncate">
//           <span className={`${!email.read ? 'font-semibold' : 'font-normal'} text-gray-900`}>
//             {email.subject}
//           </span>
//           <span className="text-gray-600 ml-2 font-normal">
//             - {email.preview}
//           </span>
//         </div>
//       </div>

//       {/* Attachments */}
//       {email.attachments && email.attachments > 0 && (
//         <div className="mr-4">
//           <Paperclip size={16} className="text-gray-400" />
//         </div>
//       )}

//       {/* Labels */}
//       {email.labels && email.labels.length > 0 && (
//         <div className="flex gap-1 mr-4">
//           {email.labels.slice(0, 2).map((label) => (
//             <span
//               key={label}
//               className="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded-full"
//             >
//               {label}
//             </span>
//           ))}
//         </div>
//       )}

//       {/* Timestamp */}
//       <div className="text-sm text-gray-500 whitespace-nowrap">
//         {email.timestamp}
//       </div>
//     </div>
//   );
// };

// export default EmailItem;




import React from 'react';
import { Star, Paperclip } from 'lucide-react';
import { Email } from '../../types/email';

interface EmailItemProps {
  email: Email;
  isSelected: boolean;
  onSelect: (emailId: number) => void;
  onToggleStar: (emailId: number) => void;
  onClick: (email: Email) => void;
}

const EmailItem: React.FC<EmailItemProps> = ({
  email,
  isSelected,
  onSelect,
  onToggleStar,
  onClick,
}) => {
  const handleStarClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleStar(email.id);
  };

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(email.id);
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // This will be called after the click event, but we've already handled the logic in onClick
  };

  function formatEmailDate(dateString: string) {
  const date = new Date(dateString);
  const now = new Date();

  // If today, show time (e.g., 11:28 AM)
  if (
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear()
  ) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  // If this year, show "Jun 30"
  if (date.getFullYear() === now.getFullYear()) {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  }

  // Otherwise, show "6/30/2024"
  return date.toLocaleDateString();
}

  return (
    <div
      className={`flex items-center px-4 py-3 border-b border-gray-100 hover:shadow-sm cursor-pointer transition-all duration-150 ${
        !email.read ? 'bg-white font-medium' : 'bg-gray-50'
      } ${isSelected ? 'bg-blue-50 border-blue-200' : ''}`}
      onClick={() => onClick(email)}
    >
      {/* Checkbox */}
      <div className="flex items-center mr-4">
        <input
          type="checkbox"
          checked={isSelected}
          onClick={handleCheckboxClick}
          onChange={handleCheckboxChange}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      </div>

      {/* Star */}
      <button
        onClick={handleStarClick}
        className="mr-4 p-1 hover:bg-gray-200 rounded transition-colors"
      >
       
        <Star
          size={16}
          color={email.starred ? '#facc15' : '#9ca3af'} // yellow-400 or gray-400
          fill={email.starred ? '#facc15' : 'none'}
          className="transition-colors"
        />
      </button>

      {/* Important marker */}
      {/* {email.important && (
        <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
      )} */}

      {/* Sender */}
      <div className="w-48 truncate mr-4">
        <span className={`${!email.read ? 'font-semibold' : 'font-normal'} text-gray-900`}>
          {email.sender}
        </span>
      </div>

      {/* compliant and Preview */}
      <div className="flex-1 min-w-0 mr-4">
        <div className="truncate">
          <span className={`${!email.read ? 'font-semibold' : 'font-normal'} text-gray-900`}>
            {email.complaint} 
          </span>
          <span className="text-gray-600 ml-2 font-normal">
            - {email.content}
          </span>
        </div>
      </div>

      {/* Attachments */}
      {/* {email.attachments && email.attachments > 0 && (
        <div className="mr-4">
          <Paperclip size={16} className="text-gray-400" />
        </div>
      )} */}

      {/* Labels */}
      {/* {email.labels && email.labels.length > 0 && (
        <div className="flex gap-1 mr-4">
          {email.labels.slice(0, 2).map((label) => (
            <span
              key={label}
              className="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded-full"
            >
              {label}
            </span>
          ))}
        </div>
      )} */}

      {/* Timestamp */}
      {/* <div className="text-sm text-gray-500 whitespace-nowrap">
        {email.created_at}
      </div> */}

      <div className="text-sm text-gray-500 whitespace-nowrap">
  {formatEmailDate(email.created_at)}
</div> 
    </div>
  );
};

export default EmailItem;
