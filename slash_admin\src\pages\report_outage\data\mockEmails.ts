import { Email } from '../../../types/email';

export const mockEmails: Email[] = [
  {
    id: 1,
    read: false,
    starred: true,
    sender: '<PERSON>',
    subject: 'Meeting Tomorrow - Q4 Planning Session',
    preview: 'Hi there, just a reminder about our meeting tomorrow at 10 AM. We\'ll be discussing the Q4 roadmap and budget allocations...',
    timestamp: '10:30 AM',
    content: `Hi there,

Just a reminder about our meeting tomorrow at 10 AM. We'll be discussing the Q4 roadmap and budget allocations.

Please come prepared with:
- Your team's Q4 objectives
- Budget requirements
- Resource allocation requests

Looking forward to seeing everyone there!

Best regards,
<PERSON>`,
    category: 'primary',
    important: true,
    labels: ['work', 'meeting'],
    attachments: 2,
  },
  {
    id: 2,
    read: false,
    starred: false,
    sender: 'Amazon',
    subject: 'Your Amazon order has shipped',
    preview: 'Your recent order #12345 has been shipped and will arrive by tomorrow. Track your package using the link below...',
    timestamp: 'Yesterday',
    content: `Dear Customer,

Your recent order #12345 has been shipped and will arrive by tomorrow.

Order Details:
- MacBook Pro 16" M3
- Delivery Address: 123 Main St
- Tracking Number: 1Z999AA1234567890

Track your package: [Track Package]

Thank you for shopping with Amazon!`,
    category: 'promotions',
    important: false,
    labels: ['shopping'],
  },
  {
    id: 3,
    read: false,
    starred: false,
    sender: 'LinkedIn',
    subject: 'You have 5 new connection requests',
    preview: 'You have 5 new connection requests waiting for your response. Connect with professionals in your network...',
    timestamp: 'Mar 15',
    content: `Hi there,

You have 5 new connection requests waiting for your response:

1. <PERSON> - Product Manager at Tech Corp
2. Mike Chen - Software Engineer at StartupXYZ
3. Lisa Wang - UX Designer at Design Studio
4. David Brown - Marketing Director at Growth Co
5. Emma Davis - Data Scientist at AI Labs

Connect with these professionals to expand your network!

Best,
The LinkedIn Team`,
    category: 'social',
    important: false,
    labels: ['networking'],
  },
  {
    id: 4,
    read: true,
    starred: true,
    sender: 'Sarah Johnson',
    subject: 'Project Update: Q2 Goals Achievement',
    preview: 'Here\'s the latest update on our Q2 goals and objectives. We\'ve made significant progress across all key metrics...',
    timestamp: 'Mar 14',
    content: `Hi Team,

Here's the latest update on our Q2 goals and objectives. We've made significant progress across all key metrics:

✅ Revenue Target: 105% achieved
✅ User Growth: 120% of target
✅ Product Launches: 3/3 completed on time
⚠️ Customer Satisfaction: 92% (target: 95%)

Next steps:
- Focus on customer satisfaction improvements
- Prepare Q3 planning documents
- Schedule team retrospective

Great work everyone!

Sarah`,
    category: 'primary',
    important: true,
    labels: ['work', 'project-update'],
  },
  {
    id: 5,
    read: false,
    starred: false,
    sender: 'Netflix',
    subject: 'New shows added to your list',
    preview: 'Check out the new shows we\'ve added based on your preferences. From thrilling dramas to comedy specials...',
    timestamp: 'Mar 12',
    content: `Hi there,

Check out the new shows we've added based on your preferences:

🎬 New This Week:
- "The Crown" Season 6
- "Stranger Things" Behind the Scenes
- "Chef's Table: Pizza"
- "Wednesday" Season 2

🔥 Trending Now:
- "The Night Agent"
- "Ginny & Georgia"
- "You" Season 4

Happy watching!
The Netflix Team`,
    category: 'promotions',
    important: false,
    labels: ['entertainment'],
  },
  {
    id: 6,
    read: true,
    starred: false,
    sender: 'GitHub',
    subject: 'Security alert: New sign-in from Chrome on Windows',
    preview: 'We detected a new sign-in to your GitHub account from a Chrome browser on Windows. If this was you, you can ignore this email...',
    timestamp: 'Mar 10',
    content: `Hi there,

We detected a new sign-in to your GitHub account:

Device: Chrome on Windows
Location: San Francisco, CA
Time: March 10, 2024 at 2:30 PM PST

If this was you, you can ignore this email. If not, please secure your account immediately.

Stay safe,
GitHub Security Team`,
    category: 'updates',
    important: true,
    labels: ['security'],
  },
  {
    id: 7,
    read: false,
    starred: false,
    sender: 'Spotify',
    subject: 'Your Discover Weekly is ready',
    preview: 'Your personalized playlist is here! This week we\'ve found 30 songs we think you\'ll love based on your listening history...',
    timestamp: 'Mar 8',
    content: `Hey Music Lover,

Your personalized Discover Weekly playlist is here! This week we've found 30 songs we think you'll love.

🎵 Featured Artists:
- Arctic Monkeys
- Tame Impala
- The Strokes
- Mac DeMarco

Listen now and let us know what you think!

Keep discovering,
Spotify`,
    category: 'social',
    important: false,
    labels: ['music'],
  },
  {
    id: 8,
    read: true,
    starred: false,
    sender: 'Apple',
    subject: 'Your iCloud storage is almost full',
    preview: 'You\'re using 4.8 GB of your 5 GB iCloud storage. Upgrade your storage plan to keep your photos, documents, and data safe...',
    timestamp: 'Mar 5',
    content: `Hi there,

You're using 4.8 GB of your 5 GB iCloud storage.

What's using your storage:
- Photos: 3.2 GB
- Documents: 1.1 GB
- Backups: 0.5 GB

Upgrade to iCloud+ for more storage and premium features:
- 50 GB: $0.99/month
- 200 GB: $2.99/month
- 2 TB: $9.99/month

Manage your storage in Settings.

Apple`,
    category: 'updates',
    important: false,
    labels: ['storage'],
  },
];