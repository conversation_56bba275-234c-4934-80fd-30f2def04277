/**
 * EmailToolbar Component
 *
 * Enhanced email toolbar with improved search functionality, accessibility,
 * and better user experience for managing email reports.
 *
 * Features:
 * - Real-time search with debouncing
 * - Keyboard shortcuts (Enter to search, Esc to clear)
 * - Advanced filtering options
 * - Bulk actions for selected emails
 * - Responsive design
 * - Accessibility support
 */







import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronDown, 
  RotateCcw, 
  Archive, 
  AlertTriangle, 
  Trash2, 
  MoreHorizontal,
  Tag,
  Clock,
  Mail,
  MailOpen,
  SearchIcon,
  X,
  SlidersHorizontal,
} from 'lucide-react';
import FilterModal from './FilterModal';
import { useEmailState } from './hooks/useEmailState';



interface EmailToolbarProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: (selectAll: boolean) => void;
  onRefresh: () => void;
  onArchive: () => void;
  onDelete: () => void;
  onMarkAsRead: () => void;
  onMarkAsUnread: () => void;
  allSelected: boolean;
}

const initialFilters = {
  sender: '',
  phone: '',
  canumber: '',
  address: '',
  complaint: '',
  includesWords: '',
  dateWithin: '1 day',
};

const EmailToolbar: React.FC<EmailToolbarProps> = ({
  selectedCount,
  totalCount,
  onSelectAll,
  onRefresh,
  onArchive,
  onDelete,
  onMarkAsRead,
  onMarkAsUnread,
  allSelected,
}) => {

  const { actions, state } = useEmailState();

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [moreActionsOpen, setMoreActionsOpen] = useState(false);
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState(initialFilters);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const moreActionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
      if (moreActionsRef.current && !moreActionsRef.current.contains(event.target as Node)) {
        setMoreActionsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelectChange = (value: string) => {
    switch (value) {
      case 'all':
        onSelectAll(true);
        break;
      case 'none':
        onSelectAll(false);
        break;
      case 'read':
        onMarkAsRead();
        // Implementation for selecting only read emails
        break;
      case 'unread':
        // Implementation for selecting only unread emails
        onMarkAsUnread();
        break;
    }
    setDropdownOpen(false);
  };

  // Local search input state (separate from global search state)
  const [searchInput, setSearchInput] = useState(state.searchQuery);

  // Update local input when global search changes (e.g., when cleared)
  useEffect(() => {
    setSearchInput(state.searchQuery);
  }, [state.searchQuery]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Apply search only on Enter - this will trigger server-side filtering
      console.log('🔍 ENTER PRESSED - Searching for:', searchInput);
      console.log('🔍 Calling actions.setSearchQuery with:', searchInput);
      actions.setSearchQuery(searchInput);
    } else if (e.key === 'Escape') {
      // Clear search on Escape
      console.log('🔍 ESCAPE PRESSED - Clearing search');
      setSearchInput('');
      actions.setSearchQuery('');
    }
  };

  const handleClearSearch = () => {
    setSearchInput('');
    actions.setSearchQuery('');
  };

// Enhanced filter handler for FilterModal
const handleFilterModalApply = (filters: typeof initialFilters) => {
  console.log('Applied filters:', filters);
  setAppliedFilters(filters);
  // TODO: Implement advanced filtering logic
};

  return (
    <div className="bg-white">
      {/* Enhanced Search Bar */}
      <div className="flex items-center px-4 py-3 border-b border-gray-200">
        <div className="relative flex-grow max-w-2xl">
          {/* Search Icon */}
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon size={18} className="text-gray-500" />
          </div>

          {/* Search Input */}
          <input
            placeholder="Search reports by sender, phone, address, complaint... (Press Enter to search)"
            value={searchInput}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            type="text"
            className="
              block w-full pl-10 pr-20 py-2.5 rounded-lg border border-gray-300
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
              text-sm text-gray-700 placeholder-gray-400
              transition duration-200 ease-in-out
              hover:border-gray-400
            "
            aria-label="Search reports (Press Enter to search)"
          />

          {/* Action Buttons */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 gap-2">
            {/* Clear Search Button */}
            {searchInput && (
              <button
                onClick={handleClearSearch}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                title="Clear search (Esc)"
                aria-label="Clear search"
              >
                <X size={16} className="text-gray-500 hover:text-gray-700" />
              </button>
            )}

            {/* Advanced Filter Button */}
            <button
              onClick={() => setFilterModalOpen(true)}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              title="Advanced filters"
              aria-label="Open advanced filters"
            >
              <SlidersHorizontal size={16} className="text-gray-500 hover:text-blue-600" />
            </button>
          </div>
        </div>

        {/* Search Results Count */}
        {state.searchQuery && (
          <div className="ml-4 text-sm text-gray-600 whitespace-nowrap">
            {totalCount} result{totalCount !== 1 ? 's' : ''}
          </div>
        )}
      </div>

      {/* Action Bar */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center gap-3">
          {/* Enhanced Select Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={allSelected}
                onChange={(e) => onSelectAll(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2 h-4 w-4"
                aria-label="Select all emails"
              />
              <button
                onClick={() => setDropdownOpen(!dropdownOpen)}
                className="p-1.5 hover:bg-gray-200 rounded-md transition-colors"
                title="Selection options"
                aria-label="Selection options"
              >
                <ChevronDown size={16} className="text-gray-600" />
              </button>
            </div>

          {dropdownOpen && (
            <div className="absolute left-0 top-full mt-2 w-44 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
              <div className="py-1">
                <button
                  onClick={() => handleSelectChange('all')}
                  className="w-full text-left px-4 py-2.5 text-sm hover:bg-blue-50 hover:text-blue-700 transition-colors flex items-center gap-2"
                >
                  <span className="w-4 h-4 border border-gray-300 rounded bg-blue-600"></span>
                  Select All
                </button>
                <button
                  onClick={() => handleSelectChange('none')}
                  className="w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <span className="w-4 h-4 border border-gray-300 rounded"></span>
                  Select None
                </button>
                <div className="border-t border-gray-100 my-1"></div>
                <button
                  onClick={() => handleSelectChange('read')}
                  className="w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <MailOpen size={16} className="text-gray-500" />
                  Read Only
                </button>
                <button
                  onClick={() => handleSelectChange('unread')}
                  className="w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <Mail size={16} className="text-gray-500" />
                  Unread Only
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Selection Status */}
        {selectedCount > 0 && (
          <div className="flex items-center gap-2 ml-3 px-3 py-1.5 bg-blue-50 rounded-lg border border-blue-200">
            <span className="text-sm font-medium text-blue-700">
              {selectedCount} selected
            </span>
            {selectedCount === totalCount && (
              <span className="text-xs text-blue-600">(all)</span>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center gap-1 ml-4">
          <button
            onClick={onRefresh}
            className="p-2 hover:bg-gray-200 rounded-md transition-colors group"
            title="Refresh emails"
            aria-label="Refresh emails"
          >
            <RotateCcw size={16} className="text-gray-600 group-hover:text-gray-800" />
          </button>

          {selectedCount > 0 && (
            <>
              {/* Separator */}
              <div className="w-px h-6 bg-gray-300 mx-2"></div>

              {/* Primary Actions */}
              <button
                onClick={onArchive}
                className="p-2 hover:bg-gray-200 rounded-md transition-colors group"
                title="Archive selected emails"
                aria-label="Archive selected emails"
              >
                <Archive size={16} className="text-gray-600 group-hover:text-gray-800" />
              </button>

              <button
                onClick={onDelete}
                className="p-2 hover:bg-red-100 rounded-md transition-colors group"
                title="Delete selected emails"
                aria-label="Delete selected emails"
              >
                <Trash2 size={16} className="text-gray-600 group-hover:text-red-600" />
              </button>

              <button
                onClick={onMarkAsRead}
                className="p-2 hover:bg-gray-200 rounded-md transition-colors group"
                title="Mark as read"
                aria-label="Mark selected emails as read"
              >
                <MailOpen size={16} className="text-gray-600 group-hover:text-green-600" />
              </button>

              <button
                onClick={onMarkAsUnread}
                className="p-2 hover:bg-gray-200 rounded-md transition-colors group"
                title="Mark as unread"
                aria-label="Mark selected emails as unread"
              >
                <Mail size={16} className="text-gray-600 group-hover:text-blue-600" />
              </button>

              {/* More Actions Dropdown */}
              <div className="relative" ref={moreActionsRef}>
                <button
                  onClick={() => setMoreActionsOpen(!moreActionsOpen)}
                  className="p-2 hover:bg-gray-200 rounded-md transition-colors group"
                  title="More actions"
                  aria-label="More actions"
                >
                  <MoreHorizontal size={16} className="text-gray-600 group-hover:text-gray-800" />
                </button>

                {moreActionsOpen && (
                  <div className="absolute right-0 top-full mt-2 w-52 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="py-1">
                      <button className="w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors flex items-center gap-3">
                        <Tag size={16} className="text-gray-500" />
                        Add Label
                      </button>
                      <button className="w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors flex items-center gap-3">
                        <Clock size={16} className="text-gray-500" />
                        Snooze
                      </button>
                      <div className="border-t border-gray-100 my-1"></div>
                      <button className="w-full text-left px-4 py-2.5 text-sm hover:bg-red-50 hover:text-red-700 transition-colors flex items-center gap-3">
                        <AlertTriangle size={16} className="text-red-500" />
                        Mark as Spam
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Enhanced Email Count & Status */}
      <div className="flex items-center gap-4">
        {/* Applied Filters Indicator */}
        {(state.searchQuery || Object.values(appliedFilters).some(v => v !== '' && v !== '1 day')) && (
          <div className="flex items-center gap-2 text-sm text-blue-600">
            <SlidersHorizontal size={14} />
            <span>Filtered</span>
          </div>
        )}

        {/* Email Count */}
        <div className="text-sm text-gray-600 font-medium">
          {totalCount.toLocaleString()} {totalCount === 1 ? 'email' : 'emails'}
        </div>
      </div>
    </div>

    {filterModalOpen && (
        <FilterModal
          isOpen={filterModalOpen}
          onClose={() => setFilterModalOpen(false)}
          onApplyFilter={handleFilterModalApply}
        />
      )}
    </div>
  );
};

export default EmailToolbar;