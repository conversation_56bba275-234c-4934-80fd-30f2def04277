// import React, { useEffect, useRef, useState } from 'react';
// import { FaChevronDown, FaRedo, FaArchive, FaExclamationCircle, FaTrash, FaEllipsisV } from 'react-icons/fa';

// const EmailToolbar = () => {

//    const [dropdownOpen, setDropdownOpen] = useState(false);
//   const dropdownRef = useRef<HTMLDivElement>(null);

//   // Close dropdown if clicked outside
//   useEffect(() => {
//     function handleClickOutside(event: MouseEvent) {
//       if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
//         setDropdownOpen(false);
//       }
//     }
//     document.addEventListener('mousedown', handleClickOutside);
//     return () => document.removeEventListener('mousedown', handleClickOutside);
//   }, []);

//   return (
//     <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200">
//       <div className="flex items-center">
//         {/* <div className="flex items-center mr-4">
//           <input type="checkbox" id="select-all" className="rounded text-blue-500 mr-2" />
//           <button
//             className="p-2 rounded hover:bg-gray-100"
//             id="select-menu"
//             onClick={() => setDropdownOpen((open) => !open)}
//             type="button"
//           >
//             <FaChevronDown className="text-gray-600 text-xs" />
//           </button>
//           {dropdownOpen && (
//             <div className="absolute left-0 mt-2 w-32 bg-white border border-gray-200 rounded shadow-lg z-10">
//               <ul className="py-1 text-sm text-gray-700">
//                 <li>
//                   <button className="w-full text-left px-4 py-2 hover:bg-gray-100">All</button>
//                 </li>
//                 <li>
//                   <button className="w-full text-left px-4 py-2 hover:bg-gray-100">None</button>
//                 </li>
//                 <li>
//                   <button className="w-full text-left px-4 py-2 hover:bg-gray-100">Read</button>
//                 </li>
//                 <li>
//                   <button className="w-full text-left px-4 py-2 hover:bg-gray-100">Unread</button>
//                 </li>
//               </ul>
//             </div>
//           )}
//         </div> */}
//         <div className="flex items-center mr-4 relative" ref={dropdownRef}>
//   <input type="checkbox" id="select-all" className="rounded text-blue-500 mr-2" />
//   <button
//     className="p-2 rounded hover:bg-gray-100"
//     id="select-menu"
//     onClick={() => setDropdownOpen((open) => !open)}
//     type="button"
//   >
//     <FaChevronDown className="text-gray-600 text-xs" />
//   </button>
//   {dropdownOpen && (
//   <div className="absolute left-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded shadow-lg z-50">
//     <ul className="py-1 text-sm text-gray-700 z-50 bg-gray-100">
//       <li>
//         <button className="w-full text-left px-4 py-2 hover:bg-gray-100 z-50">All</button>
//       </li>
//       <li>
//         <button className="w-full text-left px-4 py-2 hover:bg-gray-100">None</button>
//       </li>
//       <li>
//         <button className="w-full text-left px-4 py-2 hover:bg-gray-100">Read</button>
//       </li>
//       <li>
//         <button className="w-full text-left px-4 py-2 hover:bg-gray-100">Unread</button>
//       </li>
//     </ul>
//   </div>
// )}
// </div>
//         <button className="p-2 rounded hover:bg-gray-100 mr-1" id="refresh-btn">
//           <FaRedo className="text-gray-600" />
//         </button>
//         <button className="p-2 rounded hover:bg-gray-100 mr-1" id="archive-btn" title="Archive">
//           <i className="fas fa-archive text-gray-600"></i>
//         </button>
//         <button className="p-2 rounded hover:bg-gray-100 mr-1" id="report-spam-btn" title="Report Spam">
//           <FaExclamationCircle className="text-gray-600" />
//         </button>
//         <button className="p-2 rounded hover:bg-gray-100 mr-1" id="delete-btn" title="Delete">
//           <FaTrash className="text-gray-600" />
//         </button>
//         <button className="p-2 rounded hover:bg-gray-100" id="more-actions-btn">
//           <FaEllipsisV className="text-gray-600" />
//         </button>
//       </div>
//     </div>
//   );
// };

// export default EmailToolbar;







import React, { useState, useRef, useEffect, useMemo } from 'react';
import { 
  ChevronDown, 
  RotateCcw, 
  Archive, 
  AlertTriangle, 
  Trash2, 
  MoreHorizontal,
  Tag,
  Clock,
  Mail,
  MailOpen,
  SearchIcon,
  X,
  SlidersHorizontal,
} from 'lucide-react';
import FilterModal from './FilterModal'; 
import { useEmailState } from './hooks/useEmailState';
import { debounce } from 'lodash';



interface EmailToolbarProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: (selectAll: boolean) => void;
  onRefresh: () => void;
  onArchive: () => void;
  onDelete: () => void;
  onMarkAsRead: () => void;
  onMarkAsUnread: () => void;
  allSelected: boolean;
}

const initialFilters = {
  sender: '',
  phone: '',
  canumber: '',
  address: '',
  complaint: '',
  includesWords: '',
  dateWithin: '1 day',
};

const EmailToolbar: React.FC<EmailToolbarProps> = ({
  selectedCount,
  totalCount,
  onSelectAll,
  onRefresh,
  onArchive,
  onDelete,
  onMarkAsRead,
  onMarkAsUnread,
  allSelected,
}) => {

  const { actions, state } = useEmailState();

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [moreActionsOpen, setMoreActionsOpen] = useState(false);
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState(initialFilters);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const moreActionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
      if (moreActionsRef.current && !moreActionsRef.current.contains(event.target as Node)) {
        setMoreActionsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelectChange = (value: string) => {
    switch (value) {
      case 'all':
        onSelectAll(true);
        break;
      case 'none':
        onSelectAll(false);
        break;
      case 'read':
        onMarkAsRead();
        // Implementation for selecting only read emails
        break;
      case 'unread':
        // Implementation for selecting only unread emails
        onMarkAsUnread();
        break;
    }
    setDropdownOpen(false);
  };

  // const handleApplyFilter = (filters: typeof initialFilters) => {
  //   console.log('Applied filters:', filters);
  //   setAppliedFilters(filters);
  //   // TODO: Call API or filter local data based on `filters`
  // };

  const debouncedSearch = useMemo(
  () =>
    debounce((query) => {
      actions.setSearchQuery(query);
    }, 300),
  [actions]
);

const handleApplyFilter = (filters: { searchQuery?: string }) => {
  if (filters.searchQuery !== undefined) {
    // console.log('qqqqqqqqqqqqfilters:', filters);
    debouncedSearch(filters.searchQuery);
  }
};

  console.log('appliedFilters', appliedFilters);

  return (
    <div>
      {/* <div className="flex  justify-between items-center p-4 border-b border-gray-200 bg-white text-gray-800 text-xl">
            <SearchIcon />
            <input placeholder="Search mail" type="text" />
            <ChevronDown size={16} className="header__inputCaret" />
            
        </div> */}
        <div className="flex items-center px-4 py-2 border-b border-gray-200 bg-white">
  <div className="relative flex-grow max-w-xl">
    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      {/* Search Icon */}
      <SearchIcon size={18} className="text-gray-500" />
    </div>
    <input
      placeholder="Search report"
      value={state.searchQuery}
      onChange={(e) => handleApplyFilter({ searchQuery: e.target.value })}
      type="text"
      className="
        block w-full pl-10 pr-3 py-2 rounded-md border border-gray-300
        focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500
        text-base text-gray-700 placeholder-gray-400
        transition duration-150 ease-in-out
      "
    />
    <button className="absolute inset-y-0 right-0 pr-3 flex items-center gap-6">
      <X size={16} className="text-gray-500" />
      <SlidersHorizontal size={16} 
       onClick={() => setFilterModalOpen(true)}
      className="text-gray-500" />
    </button>
  </div>
</div>
    
    <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-white">
      <div className="flex items-center gap-2">
        {/* Select Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={allSelected}
              onChange={(e) => onSelectAll(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
            />
            <button
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="p-1 hover:bg-gray-100 rounded transition-colors"
            >
              <ChevronDown size={16} className="text-gray-600" />
            </button>
          </div>

          {dropdownOpen && (
            <div className="absolute left-0 top-full mt-1 w-40 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
              <div className="py-1">
                <button
                  onClick={() => handleSelectChange('all')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors"
                >
                  All
                </button>
                <button
                  onClick={() => handleSelectChange('none')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors"
                >
                  None
                </button>
                <button
                  onClick={() => handleSelectChange('read')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors"
                >
                  Read
                </button>
                <button
                  onClick={() => handleSelectChange('unread')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors"
                >
                  Unread
                </button>
              </div>
            </div>
          )}
        </div>

        {selectedCount > 0 && (
          <span className="text-sm text-gray-600 ml-2">
            {selectedCount} selected
          </span>
        )}

        {/* Action Buttons */}
        <div className="flex items-center gap-1 ml-4">
          <button
            onClick={onRefresh}
            className="p-2 hover:bg-gray-100 rounded transition-colors"
            title="Refresh"
          >
            <RotateCcw size={16} className="text-gray-600" />
          </button>

          {selectedCount > 0 && (
            <>
              <button
                onClick={onArchive}
                className="p-2 hover:bg-gray-100 rounded transition-colors"
                title="Archive"
              >
                <Archive size={16} className="text-gray-600" />
              </button>

              <button
                onClick={onDelete}
                className="p-2 hover:bg-gray-100 rounded transition-colors"
                title="Delete"
              >
                <Trash2 size={16} className="text-gray-600" />
              </button>

              <button
                onClick={onMarkAsRead}
                className="p-2 hover:bg-gray-100 rounded transition-colors"
                title="Mark as read"
              >
                <MailOpen size={16} className="text-gray-600" />
              </button>

              <button
                onClick={onMarkAsUnread}
                className="p-2 hover:bg-gray-100 rounded transition-colors"
                title="Mark as unread"
              >
                <Mail size={16} className="text-gray-600" />
              </button>

              <div className="relative" ref={moreActionsRef}>
                <button
                  onClick={() => setMoreActionsOpen(!moreActionsOpen)}
                  className="p-2 hover:bg-gray-100 rounded transition-colors"
                  title="More actions"
                >
                  <MoreHorizontal size={16} className="text-gray-600" />
                </button>

                {moreActionsOpen && (
                  <div className="absolute left-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="py-1">
                      <button className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors flex items-center gap-2">
                        <Tag size={14} />
                        Add label
                      </button>
                      <button className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors flex items-center gap-2">
                        <Clock size={14} />
                        Snooze
                      </button>
                      <button className="w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors flex items-center gap-2">
                        <AlertTriangle size={14} />
                        Mark as spam
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Email Count */}
      <div className="text-sm text-gray-500">
        {totalCount} emails
      </div>
    </div>

    {filterModalOpen && (
        <FilterModal
          isOpen={filterModalOpen}
          onClose={() => setFilterModalOpen(false)}
          onApplyFilter={handleApplyFilter}
        />
      )}
    </div>
  );
};

export default EmailToolbar;