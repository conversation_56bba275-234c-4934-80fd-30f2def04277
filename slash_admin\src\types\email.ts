export interface Email {
  id: number;
  read: boolean;
  starred: boolean;
  sender: string;
  phone: string;
  address: string;
  canumber: string;
  // subject: string;
  complaint: string;
  // preview: string;
  // timestamp: string;
  content: string;
  // category: 'primary' | 'social' | 'promotions' | 'updates';
  // important: boolean;
  // labels: string[];
  // attachments?: number;
  requestNumber?: string;
  remark?: string;
  status?: string;
  created_at: string;
  updated_at: string;
}

export interface EmailState {
  emails: Email[];
  selectedEmails: Set<number>;
  currentView: 'inbox' | 'starred' | 'sent' | 'drafts' | 'trash';
  searchQuery: string;
  selectedEmail: Email | null;
  showCompose: boolean;
}