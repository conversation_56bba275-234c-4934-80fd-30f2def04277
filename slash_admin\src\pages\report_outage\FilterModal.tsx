import React, { useState } from 'react';
import { XCircle, CheckCircle } from 'lucide-react';

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilter: (filters: typeof initialFilters) => void;
}

const initialFilters = {
  sender: '',
  phone: '',
  canumber: '',
  address: '',
  complaint: '',
  includesWords: '',
  dateWithin: '1 day',
  customDate: '',
};

const FilterModal: React.FC<FilterModalProps> = ({ isOpen, onClose, onApplyFilter }) => {

  const [filterValues, setFilterValues] = useState(initialFilters);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilterValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div
      className={`fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onClick={onClose}
    >
      <div
        className="absolute bg-[#FFFFFF] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg p-6 w-96"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="text-lg font-semibold mb-4">Search options</h2>

        {/* Search Fields */}
        <div className="mb-2">
          <label htmlFor="sender" className="block text-sm font-medium text-gray-700">
            Sender Name
          </label>
          <input
            type="text"
            id="sender"
            name="sender"
            value={filterValues.sender}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="mb-2">
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
            Phone
          </label>
          <input
            type="text"
            id="phone"
            name="phone"
            value={filterValues.phone}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>


        <div className="mb-2">
          <label htmlFor="canumber" className="block text-sm font-medium text-gray-700">
            CA Number or BP Number
          </label>
          <input
            type="text"
            id="canumber"
            name="canumber"
            value={filterValues.canumber}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="mb-2">
          <label htmlFor="address" className="block text-sm font-medium text-gray-700">
            Address
          </label>
          <input
            type="text"
            id="address"
            name="address"
            value={filterValues.address}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>



        <div className="mb-2">
          <label htmlFor="complaint" className="block text-sm font-medium text-gray-700">
            Complaint
          </label>
          <input
            type="text"
            id="complaint"
            name="complaint"
            value={filterValues.complaint}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="mb-2">
          <label htmlFor="includesWords" className="block text-sm font-medium text-gray-700">
            Includes the words
          </label>
          <input
            type="text"
            id="includesWords"
            name="includesWords"
            value={filterValues.includesWords}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Date Within */}
        <div className="mb-2 flex items-center gap-2">
          <label htmlFor="dateWithin" className="block text-sm font-medium text-gray-700">
            Date within
          </label>
          <select
            id="dateWithin"
            name="dateWithin"
            value={filterValues.dateWithin}
            onChange={handleInputChange}
            className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1 day">1 day</option>
            <option value="3 days">3 days</option>
            <option value="1 week">1 week</option>
            <option value="2 weeks">2 weeks</option>
            <option value="1 month">1 month</option>
          </select>
          <input
            type="date"
            id="customDate"
            name="customDate"
            value={filterValues.customDate}
            onChange={handleInputChange}
            className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>


        {/* Actions */}
        <div className="mt-4 flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              console.log('🔍 FilterModal: Applying filter values:', filterValues);
              onApplyFilter(filterValues);
              onClose();
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Apply Filter
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterModal;