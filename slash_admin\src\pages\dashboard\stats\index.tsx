import React, { useState } from 'react';
import { Tabs, Typography } from 'antd';
import InspectionStatus from './InspectionStatus';
import GeneralStatus from './GeneralStatus';

const { TabPane } = Tabs;

const Stats: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');

  return (
    <div className="w-full p-4">
      <Typography.Title level={2}>Hi, Welcome back 👋</Typography.Title>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="General" key="general">
          <GeneralStatus />
        </TabPane>
        <TabPane tab="Inspection Status" key="inspection">
          <InspectionStatus />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Stats;
