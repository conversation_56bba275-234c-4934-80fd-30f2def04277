import React, { useState, useEffect } from 'react';
import { Form, Row, Col, Select, Button, Input, DatePicker } from 'antd';
import type { Moment } from 'moment';
import orgService from "@/api/services/orgService";

interface FilterFormProps {
  filters: Record<string, any>;
  onFilterChange: (values: Record<string, any>) => void;
  onReset: () => void;
  onApply: () => void;
}

const { RangePicker } = DatePicker;

// Define the choice constants
const SERVICE_TYPE_CHOICES = [
  { value: 'Dedicated', label: 'Dedicated' },
  { value: 'Public', label: 'Public' },
];

const CONDITION_CHOICES = [
  { value: 'Good', label: 'Good' },
  { value: 'Fair', label: 'Fair' },
  { value: 'Poor', label: 'Poor' },
];

const STATUS_CHOICES = [
  { value: 'Ok', label: 'Ok' },
  { value: 'one missed', label: 'One Missed' },
  { value: 'two missed', label: 'Two Missed' },
  { value: 'all missed', label: 'All Missed' },
];

const OIL_LEVEL_CHOICES = [
  { value: 'Full', label: 'Full' },
  { value: '0.75', label: '0.75' },
  { value: '0.5', label: '0.5' },
  { value: '0.25', label: '0.25' },
];

const INSULATION_LEVEL_CHOICES = [
  { value: 'Acceptable', label: 'Acceptable' },
  { value: 'Not Acceptable', label: 'Not Acceptable' },
];

const HORN_GAP_CHOICES = [
  { value: 'Good', label: 'Good' },
  { value: 'Poor', label: 'Poor' },
];

const YES_NO_CHOICES = [
  { value: 'Yes', label: 'Yes' },
  { value: 'No', label: 'No' },
];

const AVAILABLE_CHOICES = [
  { value: 'Available', label: 'Available' },
  { value: 'Not Available', label: 'Not Available' },
];

const TOTAL_TRANSFORMER_LOAD_CHOICES = [
  { value: 'below20', label: 'Below 20%' },
  { value: 'load20_50', label: '20% - 50%' },
  { value: 'load50_80', label: '50% - 80%' },
  { value: 'load80_100', label: '80% - 100%' },
  { value: 'above100', label: 'Above 100%' },
];

const VOLTAGE_STATUS_CHOICES = [
  { value: 'underVoltage', label: 'Under Voltage (< 360V)' },
  { value: 'normal', label: 'Normal (360V - 440V)' },
  { value: 'overVoltage', label: 'Over Voltage (> 440V)' },
];

const VOLTAGE_UNBALANCE_CHOICES = [
  { value: 'balanced', label: 'Balanced (≤ 3%)' },
  { value: 'unbalanced', label: 'Unbalanced (> 3%)' },
];

const FilterForm: React.FC<FilterFormProps> = ({
  filters,
  onFilterChange,
  onReset,
  onApply,
}) => {
  const [form] = Form.useForm();
  // Add state for regions and CSCs
  const [regions, setRegions] = useState<any[]>([]);
  const [selectedCSCs, setSelectedCSCs] = useState<any[]>([]);

  // Load regions when component mounts
  useEffect(() => {
    const loadRegions = async () => {
      try {
        const data = await orgService.getOrgList();
        setRegions(data);
      } catch (error) {
        console.error("Error fetching regions:", error);
      }
    };
    loadRegions();
  }, []);

  const handleValuesChange = (_: any, allValues: any) => {
    onFilterChange(allValues);
  };

  const handleReset = () => {
    // Reset all form fields to undefined
    const emptyValues = Object.keys(form.getFieldsValue()).reduce((acc, key) => {
      acc[key] = undefined;
      return acc;
    }, {} as Record<string, undefined>);
    
    // Set all form values to undefined
    form.setFieldsValue(emptyValues);
    
    // Clear local states
    setSelectedCSCs([]);
    
    // Update parent component
    onFilterChange({});
    onReset();
  };

  // Sync form with external filters
  useEffect(() => {
    if (!filters || Object.keys(filters).length === 0) {
      form.resetFields();
    } else {
      form.setFieldsValue(filters);
    }
  }, [filters, form]);

  return (
    <Form
      form={form}
      onValuesChange={handleValuesChange}
      layout="vertical"
    >
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Form.Item name="station_code" label="Station Code">
            <Input placeholder="Station Code" />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Form.Item name="region" label="Region">
            <Select
              placeholder="Select Region"
              allowClear
              showSearch
              optionFilterProp="children"
              onChange={(value) => {
                const selectedRegion = regions.find(region => region.csc_code === value);
                if (selectedRegion) {
                  setSelectedCSCs(selectedRegion.csc_centers);
                  form.setFieldsValue({ csc: undefined }); // Clear CSC when region changes
                }
              }}
            >
              {regions.map(region => (
                <Select.Option key={region.csc_code} value={region.csc_code}>
                  {region.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Form.Item name="csc" label="CSC">
            <Select
              placeholder="Select CSC"
              allowClear
              showSearch
              optionFilterProp="children"
              disabled={!form.getFieldValue('region')}
            >
              {selectedCSCs.map(csc => (
                <Select.Option key={csc.csc_code} value={csc.csc_code}>
                  {csc.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>


        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="BodyCondition" label="Body Condition">
            <Select allowClear placeholder="Select condition" options={CONDITION_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="Arrester" label="Arrester">
            <Select allowClear placeholder="Select status" options={STATUS_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="DropOutFuse" label="Drop Out">
            <Select allowClear placeholder="Select status" options={STATUS_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="FuseLink" label="Fuse Link">
            <Select allowClear placeholder="Select status" options={STATUS_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="MvBushing" label="MV Bushing">
            <Select allowClear placeholder="Select status" options={STATUS_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="LvBushing" label="LV Bushing">
            <Select allowClear placeholder="Select status" options={STATUS_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="OilLevel" label="Oil Level">
            <Select allowClear placeholder="Select level" options={OIL_LEVEL_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="InsulationLevel" label="Insulation Level">
            <Select allowClear placeholder="Select level" options={INSULATION_LEVEL_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="HornGap" label="Horn Gap">
            <Select allowClear placeholder="Select status" options={HORN_GAP_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="silica_gel" label="Silica Gel">
            <Select allowClear placeholder="Select condition" options={CONDITION_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="HasLinkage" label="Has Linkage">
            <Select allowClear placeholder="Select option" options={YES_NO_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="ArresterBodyGround" label="Arrester Body Ground">
            <Select allowClear placeholder="Select status" options={AVAILABLE_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="NeutralGround" label="Neutral Ground">
            <Select allowClear placeholder="Select status" options={AVAILABLE_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="StatusOfMounting" label="Status of Mounting">
            <Select allowClear placeholder="Select condition" options={CONDITION_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="MountingCondition" label="Mounting Condition">
            <Select allowClear placeholder="Select condition" options={CONDITION_CHOICES} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
                  <Form.Item name="total_transformer_load" label="Total Transformer Load">
                    <Select 
                      allowClear 
                      placeholder="Select transformer load status" 
                      options={TOTAL_TRANSFORMER_LOAD_CHOICES}
                    />
                  </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="average_voltage" label="Average Voltage">
            <Select 
              allowClear 
              placeholder="Select voltage status" 
              options={VOLTAGE_STATUS_CHOICES}
            />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="voltage_phase_unbalance" label="Voltage Phase Unbalance">
            <Select 
              allowClear 
              placeholder="Select unbalance status" 
              options={VOLTAGE_UNBALANCE_CHOICES}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row justify="end" gutter={[16, 16]} style={{ marginTop: '20px' }}>
        <Col>
          <Button type="default" onClick={handleReset}>Reset</Button>
        </Col>
        <Col>
          <Button type="primary" onClick={onApply}>
            Apply Filters
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default FilterForm;









