import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Email, EmailState } from '../types/email';
import reportService from '@/api/services/reportService';

// Filter interface for advanced filtering
export interface EmailFilters {
  sender?: string;
  phone?: string;
  canumber?: string;
  address?: string;
  complaint?: string;
  includesWords?: string;
  dateWithin?: string;
  customDate?: string;
}

interface EmailStore extends EmailState {
  // Loading state
  loading: boolean;

  // Filter state
  appliedFilters: EmailFilters;

  // Actions
  setEmails: (emails: Email[]) => void;
  setLoading: (loading: boolean) => void;
  setSearchQuery: (query: string) => Promise<void>;
  setFilters: (filters: EmailFilters) => Promise<void>;
  clearFilters: () => Promise<void>;
  toggleEmailSelection: (emailId: number) => void;
  selectAllEmails: (selectAll: boolean) => void;
  toggleStar: (emailId: number) => Promise<void>;
  markAsRead: (emailIds: number[]) => Promise<void>;
  markAsUnread: (emailIds: number[]) => Promise<void>;
  deleteEmails: (emailIds: number[]) => Promise<void>;
  archiveEmails: (emailIds: number[]) => Promise<void>;
  setCurrentView: (view: EmailState['currentView']) => void;
  selectEmail: (email: Email | null) => Promise<void>;
  setShowCompose: (show: boolean) => void;
  updateEmailFields: (id: number, fields: Partial<Email>) => Promise<void>;
  fetchEmails: (searchQuery?: string, filters?: EmailFilters) => Promise<void>;
  refresh: () => Promise<void>;
}

export const useEmailStore = create<EmailStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      emails: [],
      selectedEmails: new Set(),
      currentView: 'inbox',
      searchQuery: '',
      selectedEmail: null,
      showCompose: false,
      loading: false,
      appliedFilters: {},

      // Actions
      setEmails: (emails) => {
        console.log('🔄 Zustand: Setting emails:', emails.length);
        set({ emails }, false, 'setEmails');
      },

      setLoading: (loading) => {
        set({ loading }, false, 'setLoading');
      },

      fetchEmails: async (searchQuery?: string, filters?: EmailFilters) => {
        const { setLoading, setEmails, appliedFilters } = get();

        setLoading(true);
        try {
          // Combine search query and filters
          const params: any = {};

          // Add search query if provided
          if (searchQuery && searchQuery.trim()) {
            params.search = searchQuery.trim();
            console.log('🔍 Zustand: Fetching emails with search query:', searchQuery);
          }

          // Add filters (use provided filters or current applied filters)
          const currentFilters = filters || appliedFilters;
          if (currentFilters && Object.keys(currentFilters).length > 0) {
            // Add each filter to params if it has a value
            Object.entries(currentFilters).forEach(([key, value]) => {
              if (value && value.trim && value.trim() !== '') {
                params[key] = value.trim();
              } else if (value && typeof value !== 'string' && value !== '') {
                params[key] = value;
              }
            });
            console.log('🔍 Zustand: Fetching emails with filters:', currentFilters);
          }

          if (Object.keys(params).length === 0) {
            console.log('📬 Zustand: Fetching all emails (no search/filters)');
          }

          console.log('📡 Zustand: Final API params:', params);
          const response = await reportService.getReports(params);
          console.log('API Response:', response);

          const emails = response.results || response;
          console.log('📧 Zustand: Emails received from server:', emails.length);

          setEmails(emails);
        } catch (error) {
          console.error('Failed to fetch emails:', error);
        } finally {
          setLoading(false);
        }
      },

      setSearchQuery: async (query) => {
        console.log('🔄 Zustand: setSearchQuery called with:', query);

        // Update the search query in state
        set({ searchQuery: query }, false, 'setSearchQuery');

        // Fetch emails from server with the new search query
        console.log('🔄 Zustand: About to call fetchEmails with:', query);
        await get().fetchEmails(query);
        console.log('🔄 Zustand: fetchEmails completed');
      },

      setFilters: async (filters) => {
        console.log('🔄 Zustand: setFilters called with:', filters);

        // Update the applied filters in state
        set({ appliedFilters: filters }, false, 'setFilters');

        // Fetch emails from server with current search query and new filters
        const { searchQuery, fetchEmails } = get();
        console.log('🔄 Zustand: About to call fetchEmails with filters');
        await fetchEmails(searchQuery, filters);
        console.log('🔄 Zustand: fetchEmails with filters completed');
      },

      clearFilters: async () => {
        console.log('🔄 Zustand: clearFilters called');

        // Clear the applied filters in state
        set({ appliedFilters: {} }, false, 'clearFilters');

        // Fetch emails from server with current search query but no filters
        const { searchQuery, fetchEmails } = get();
        console.log('🔄 Zustand: About to call fetchEmails without filters');
        await fetchEmails(searchQuery, {});
        console.log('🔄 Zustand: fetchEmails without filters completed');
      },

      toggleEmailSelection: (emailId) => {
        const { selectedEmails } = get();
        const newSelected = new Set(selectedEmails);
        if (newSelected.has(emailId)) {
          newSelected.delete(emailId);
        } else {
          newSelected.add(emailId);
        }
        set({ selectedEmails: newSelected }, false, 'toggleEmailSelection');
      },

      selectAllEmails: (selectAll) => {
        const { emails } = get();
        set({
          selectedEmails: selectAll ? new Set(emails.map(email => email.id)) : new Set()
        }, false, 'selectAllEmails');
      },

      toggleStar: async (emailId) => {
        const { emails } = get();
        const email = emails.find(e => e.id === emailId);
        
        if (!email) return;

        try {
          await reportService.updateReport(emailId, { starred: !email.starred });
          
          set((state) => ({
            emails: state.emails.map(email =>
              email.id === emailId ? { ...email, starred: !email.starred } : email
            ),
            selectedEmail: state.selectedEmail && state.selectedEmail.id === emailId
              ? { ...state.selectedEmail, starred: !email.starred }
              : state.selectedEmail,
          }), false, 'toggleStar');
        } catch (error) {
          console.error('Failed to toggle star:', error);
        }
      },

      markAsRead: async (emailIds) => {
        console.log('📧 Marking emails as read:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to mark as read');
          return;
        }

        try {
          // Update each email on the server
          const updatePromises = emailIds.map(async (id) => {
            console.log(`📧 Updating email ${id} as read on server`);
            return await reportService.updateReport(id, { read: true });
          });

          await Promise.all(updatePromises);

          console.log('✅ Successfully marked emails as read on server');

          // Update local state after successful server update
          set((state) => ({
            emails: state.emails.map(email =>
              emailIds.includes(email.id) ? { ...email, read: true } : email
            ),
            selectedEmails: new Set(),
          }), false, 'markAsRead');

        } catch (error) {
          console.error('❌ Failed to mark emails as read:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      markAsUnread: async (emailIds) => {
        console.log('📧 Marking emails as unread:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to mark as unread');
          return;
        }

        try {
          // Update each email on the server
          const updatePromises = emailIds.map(async (id) => {
            console.log(`📧 Updating email ${id} as unread on server`);
            return await reportService.updateReport(id, { read: false });
          });

          await Promise.all(updatePromises);

          console.log('✅ Successfully marked emails as unread on server');

          // Update local state after successful server update
          set((state) => ({
            emails: state.emails.map(email =>
              emailIds.includes(email.id) ? { ...email, read: false } : email
            ),
            selectedEmails: new Set(),
          }), false, 'markAsUnread');

        } catch (error) {
          console.error('❌ Failed to mark emails as unread:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      deleteEmails: async (emailIds) => {
        console.log('🗑️ Deleting emails:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to delete');
          return;
        }

        try {
          // Delete each email on the server
          const deletePromises = emailIds.map(async (id) => {
            console.log(`🗑️ Deleting email ${id} on server`);
            return await reportService.deleteReport(id);
          });

          await Promise.all(deletePromises);

          console.log('✅ Successfully deleted emails on server');

          // Update local state after successful server deletion
          set((state) => ({
            emails: state.emails.filter(email => !emailIds.includes(email.id)),
            selectedEmails: new Set(),
          }), false, 'deleteEmails');

        } catch (error) {
          console.error('❌ Failed to delete emails:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      archiveEmails: async (emailIds) => {
        console.log('📦 Archiving emails:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to archive');
          return;
        }

        try {
          // Archive each email on the server (update archived status)
          const archivePromises = emailIds.map(async (id) => {
            console.log(`📦 Archiving email ${id} on server`);
            return await reportService.updateReport(id, { archived: true });
          });

          await Promise.all(archivePromises);

          console.log('✅ Successfully archived emails on server');

          // Update local state after successful server archiving
          // Remove from current view (they're now archived)
          set((state) => ({
            emails: state.emails.filter(email => !emailIds.includes(email.id)),
            selectedEmails: new Set(),
          }), false, 'archiveEmails');

        } catch (error) {
          console.error('❌ Failed to archive emails:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      setCurrentView: (view) => {
        set({ currentView: view, selectedEmails: new Set() }, false, 'setCurrentView');
      },

      selectEmail: async (email) => {
        console.log('🔄 Zustand: selectEmail called with:', email);

        // Update selected email in Zustand store
        set({ selectedEmail: email }, false, 'selectEmail');

        // If email is unread, mark it as read
        if (email && !email.read) {
          console.log('📧 Auto-marking email as read:', email.id);
          const { markAsRead } = get();
          await markAsRead([email.id]); // markAsRead expects an array
        }
      },

      setShowCompose: (show) => {
        set({ showCompose: show }, false, 'setShowCompose');
      },

      updateEmailFields: async (id, fields) => {
        try {
          await reportService.updateReport(id, fields);
          console.log('Email fields updated successfully');
        } catch (error) {
          console.error('Failed to update email fields:', error);
          return;
        }

        set((state) => ({
          emails: state.emails.map(email =>
            email.id === id ? { ...email, ...fields } : email
          ),
          selectedEmail: state.selectedEmail && state.selectedEmail.id === id
            ? { ...state.selectedEmail, ...fields }
            : state.selectedEmail,
        }), false, 'updateEmailFields');
      },

      refresh: async () => {
        const { searchQuery, appliedFilters, fetchEmails } = get();
        await fetchEmails(searchQuery, appliedFilters);
      },
    }),
    {
      name: 'email-store',
    }
  )
);
