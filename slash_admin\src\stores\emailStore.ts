import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Email, EmailState } from '../types/email';
import reportService from '@/api/services/reportService';

interface EmailStore extends EmailState {
  // Loading state
  loading: boolean;
  
  // Actions
  setEmails: (emails: Email[]) => void;
  setLoading: (loading: boolean) => void;
  setSearchQuery: (query: string) => Promise<void>;
  toggleEmailSelection: (emailId: number) => void;
  selectAllEmails: (selectAll: boolean) => void;
  toggleStar: (emailId: number) => Promise<void>;
  markAsRead: (emailIds: number[]) => Promise<void>;
  markAsUnread: (emailIds: number[]) => Promise<void>;
  deleteEmails: (emailIds: number[]) => Promise<void>;
  archiveEmails: (emailIds: number[]) => Promise<void>;
  setCurrentView: (view: EmailState['currentView']) => void;
  selectEmail: (email: Email | null) => Promise<void>;
  setShowCompose: (show: boolean) => void;
  updateEmailFields: (id: number, fields: Partial<Email>) => Promise<void>;
  fetchEmails: (searchQuery?: string) => Promise<void>;
  refresh: () => Promise<void>;
}

export const useEmailStore = create<EmailStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      emails: [],
      selectedEmails: new Set(),
      currentView: 'inbox',
      searchQuery: '',
      selectedEmail: null,
      showCompose: false,
      loading: false,

      // Actions
      setEmails: (emails) => {
        console.log('🔄 Zustand: Setting emails:', emails.length);
        set({ emails }, false, 'setEmails');
      },

      setLoading: (loading) => {
        set({ loading }, false, 'setLoading');
      },

      fetchEmails: async (searchQuery?: string) => {
        const { setLoading, setEmails } = get();
        
        setLoading(true);
        try {
          // Send search query to server
          const params: any = {};
          if (searchQuery && searchQuery.trim()) {
            params.search = searchQuery.trim();
            console.log('🔍 Zustand: Fetching emails with search query:', searchQuery);
          } else {
            console.log('📬 Zustand: Fetching all emails');
          }
          
          const response = await reportService.getReports(params);
          console.log('API Response:', response);
          
          const emails = response.results || response;
          console.log('📧 Zustand: Emails received from server:', emails.length);
          
          setEmails(emails);
        } catch (error) {
          console.error('Failed to fetch emails:', error);
        } finally {
          setLoading(false);
        }
      },

      setSearchQuery: async (query) => {
        console.log('🔄 Zustand: setSearchQuery called with:', query);
        
        // Update the search query in state
        set({ searchQuery: query }, false, 'setSearchQuery');
        
        // Fetch emails from server with the new search query
        console.log('🔄 Zustand: About to call fetchEmails with:', query);
        await get().fetchEmails(query);
        console.log('🔄 Zustand: fetchEmails completed');
      },

      toggleEmailSelection: (emailId) => {
        const { selectedEmails } = get();
        const newSelected = new Set(selectedEmails);
        if (newSelected.has(emailId)) {
          newSelected.delete(emailId);
        } else {
          newSelected.add(emailId);
        }
        set({ selectedEmails: newSelected }, false, 'toggleEmailSelection');
      },

      selectAllEmails: (selectAll) => {
        const { emails } = get();
        set({
          selectedEmails: selectAll ? new Set(emails.map(email => email.id)) : new Set()
        }, false, 'selectAllEmails');
      },

      toggleStar: async (emailId) => {
        const { emails } = get();
        const email = emails.find(e => e.id === emailId);
        
        if (!email) return;

        try {
          await reportService.updateReport(emailId, { starred: !email.starred });
          
          set((state) => ({
            emails: state.emails.map(email =>
              email.id === emailId ? { ...email, starred: !email.starred } : email
            ),
            selectedEmail: state.selectedEmail && state.selectedEmail.id === emailId
              ? { ...state.selectedEmail, starred: !email.starred }
              : state.selectedEmail,
          }), false, 'toggleStar');
        } catch (error) {
          console.error('Failed to toggle star:', error);
        }
      },

      markAsRead: async (emailIds) => {
        console.log('📧 Marking emails as read:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to mark as read');
          return;
        }

        try {
          // Update each email on the server
          const updatePromises = emailIds.map(async (id) => {
            console.log(`📧 Updating email ${id} as read on server`);
            return await reportService.updateReport(id, { read: true });
          });

          await Promise.all(updatePromises);

          console.log('✅ Successfully marked emails as read on server');

          // Update local state after successful server update
          set((state) => ({
            emails: state.emails.map(email =>
              emailIds.includes(email.id) ? { ...email, read: true } : email
            ),
            selectedEmails: new Set(),
          }), false, 'markAsRead');

        } catch (error) {
          console.error('❌ Failed to mark emails as read:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      markAsUnread: async (emailIds) => {
        console.log('📧 Marking emails as unread:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to mark as unread');
          return;
        }

        try {
          // Update each email on the server
          const updatePromises = emailIds.map(async (id) => {
            console.log(`📧 Updating email ${id} as unread on server`);
            return await reportService.updateReport(id, { read: false });
          });

          await Promise.all(updatePromises);

          console.log('✅ Successfully marked emails as unread on server');

          // Update local state after successful server update
          set((state) => ({
            emails: state.emails.map(email =>
              emailIds.includes(email.id) ? { ...email, read: false } : email
            ),
            selectedEmails: new Set(),
          }), false, 'markAsUnread');

        } catch (error) {
          console.error('❌ Failed to mark emails as unread:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      deleteEmails: async (emailIds) => {
        console.log('🗑️ Deleting emails:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to delete');
          return;
        }

        try {
          // Delete each email on the server
          const deletePromises = emailIds.map(async (id) => {
            console.log(`🗑️ Deleting email ${id} on server`);
            return await reportService.deleteReport(id);
          });

          await Promise.all(deletePromises);

          console.log('✅ Successfully deleted emails on server');

          // Update local state after successful server deletion
          set((state) => ({
            emails: state.emails.filter(email => !emailIds.includes(email.id)),
            selectedEmails: new Set(),
          }), false, 'deleteEmails');

        } catch (error) {
          console.error('❌ Failed to delete emails:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      archiveEmails: async (emailIds) => {
        console.log('📦 Archiving emails:', emailIds);

        if (emailIds.length === 0) {
          console.log('⚠️ No emails to archive');
          return;
        }

        try {
          // Archive each email on the server (update archived status)
          const archivePromises = emailIds.map(async (id) => {
            console.log(`📦 Archiving email ${id} on server`);
            return await reportService.updateReport(id, { archived: true });
          });

          await Promise.all(archivePromises);

          console.log('✅ Successfully archived emails on server');

          // Update local state after successful server archiving
          // Remove from current view (they're now archived)
          set((state) => ({
            emails: state.emails.filter(email => !emailIds.includes(email.id)),
            selectedEmails: new Set(),
          }), false, 'archiveEmails');

        } catch (error) {
          console.error('❌ Failed to archive emails:', error);
          console.error('Error details:', error.response?.data || error.message);
          // Optionally show user notification about the error
        }
      },

      setCurrentView: (view) => {
        set({ currentView: view, selectedEmails: new Set() }, false, 'setCurrentView');
      },

      selectEmail: async (email) => {
        console.log('🔄 Zustand: selectEmail called with:', email);

        // Update selected email in Zustand store
        set({ selectedEmail: email }, false, 'selectEmail');

        // If email is unread, mark it as read
        if (email && !email.read) {
          console.log('📧 Auto-marking email as read:', email.id);
          const { markAsRead } = get();
          await markAsRead([email.id]); // markAsRead expects an array
        }
      },

      setShowCompose: (show) => {
        set({ showCompose: show }, false, 'setShowCompose');
      },

      updateEmailFields: async (id, fields) => {
        try {
          await reportService.updateReport(id, fields);
          console.log('Email fields updated successfully');
        } catch (error) {
          console.error('Failed to update email fields:', error);
          return;
        }

        set((state) => ({
          emails: state.emails.map(email =>
            email.id === id ? { ...email, ...fields } : email
          ),
          selectedEmail: state.selectedEmail && state.selectedEmail.id === id
            ? { ...state.selectedEmail, ...fields }
            : state.selectedEmail,
        }), false, 'updateEmailFields');
      },

      refresh: async () => {
        const { searchQuery, fetchEmails } = get();
        await fetchEmails(searchQuery);
      },
    }),
    {
      name: 'email-store',
    }
  )
);
