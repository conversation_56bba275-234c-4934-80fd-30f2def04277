import axios from 'axios';

const API_BASE = '/api/report/messages'

export const reportService = {
  // Get all reports with optional search and filter parameters
  getReports: async (params?: {
    search?: string;
    sender?: string;
    phone?: string;
    canumber?: string;
    address?: string;
    complaint?: string;
    includesWords?: string;
    customDate?: string;
    [key: string]: any;
  }) => {
    console.log('📡 API: getReports called with params:', params);
    const res = await axios.get(API_BASE, { params });
    return res.data;
  },

  // Get a single report by ID
  getReportById: async (id: number | string) => {
    const res = await axios.get(`${API_BASE}/${id}`);
    return res.data;
  },

  // Create a new report
  createReport: async (data: any) => {
    const res = await axios.post(API_BASE, data);
    return res.data;
  },

  // Update a report
  updateReport: async (id: number | string, data: any) => {
    const res = await axios.patch(`${API_BASE}/${id}/`, data);
    return res.data;
  },

  // Delete a report
  deleteReport: async (id: number | string) => {
    const res = await axios.delete(`${API_BASE}/${id}/`);
    return res.data;
  },
};

export default reportService;