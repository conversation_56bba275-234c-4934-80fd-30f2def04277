import React from 'react';
import Pagination from './Pagination';

interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  totalEmails: number;
  emailsPerPage: number;
  startIndex: number;
  endIndex: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  visiblePages: number[];
  onPageChange: (page: number) => void;
  onNextPage: () => void;
  onPrevPage: () => void;
  onFirstPage: () => void;
  onLastPage: () => void;
  onItemsPerPageChange: (count: number) => void;
}

const PaginationControls: React.FC<PaginationControlsProps> = (props) => {
  return (
    <Pagination
      {...props}
      totalItems={props.totalEmails}
      itemsPerPage={props.emailsPerPage}
      showItemsPerPage={true}
      showInfo={true}
      showFirstLast={true}
      itemsPerPageOptions={[10, 25, 50, 100]}
      className="px-6 py-3"
    />
  );
};

export default PaginationControls;