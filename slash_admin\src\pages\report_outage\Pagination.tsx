import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  MoreHorizontal,
  ChevronDown
} from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  startIndex: number;
  endIndex: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  visiblePages: number[];
  onPageChange: (page: number) => void;
  onNextPage: () => void;
  onPrevPage: () => void;
  onFirstPage: () => void;
  onLastPage: () => void;
  onItemsPerPageChange: (count: number) => void;
  showItemsPerPage?: boolean;
  showInfo?: boolean;
  showFirstLast?: boolean;
  itemsPerPageOptions?: number[];
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  startIndex,
  endIndex,
  hasNextPage,
  hasPrevPage,
  visiblePages,
  onPageChange,
  onNextPage,
  onPrevPage,
  onFirstPage,
  onLastPage,
  onItemsPerPageChange,
  showItemsPerPage = true,
  showInfo = true,
  showFirstLast = true,
  itemsPerPageOptions = [10, 25, 50, 100],
  className = '',
}) => {
  const [showPageInput, setShowPageInput] = useState(false);
  const [pageInput, setPageInput] = useState('');
  const [showItemsDropdown, setShowItemsDropdown] = useState(false);
  const pageInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowItemsDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handlePageInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const page = parseInt(pageInput);
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
    setShowPageInput(false);
    setPageInput('');
  };

  const handlePageInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowPageInput(false);
      setPageInput('');
    }
  };

  useEffect(() => {
    if (showPageInput && pageInputRef.current) {
      pageInputRef.current.focus();
    }
  }, [showPageInput]);

  if (totalPages <= 1) {
    return showInfo ? (
      <div className={`flex items-center justify-between text-sm text-gray-600 ${className}`}>
        <span>
          {totalItems === 0 ? 'No items' : `${startIndex + 1}-${endIndex} of ${totalItems}`}
        </span>
      </div>
    ) : null;
  }

  return (
    <div className={`flex items-center justify-between gap-4 ${className}`}>
      {/* Items per page selector */}
      {showItemsPerPage && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show:</span>
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowItemsDropdown(!showItemsDropdown)}
              className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {itemsPerPage}
              <ChevronDown size={14} />
            </button>
            
            {showItemsDropdown && (
              <div className="absolute bottom-full mb-1 left-0 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[80px]">
                {itemsPerPageOptions.map((option) => (
                  <button
                    key={option}
                    onClick={() => {
                      onItemsPerPageChange(option);
                      setShowItemsDropdown(false);
                    }}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-md last:rounded-b-md ${
                      option === itemsPerPage ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Page info */}
      {showInfo && (
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            {startIndex + 1}-{endIndex} of {totalItems}
          </span>
          
          {/* Quick page jump */}
          {totalPages > 10 && (
            <div className="flex items-center gap-2">
              {showPageInput ? (
                <form onSubmit={handlePageInputSubmit} className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Go to:</span>
                  <input
                    ref={pageInputRef}
                    type="number"
                    min="1"
                    max={totalPages}
                    value={pageInput}
                    onChange={(e) => setPageInput(e.target.value)}
                    onKeyDown={handlePageInputKeyDown}
                    onBlur={() => {
                      setShowPageInput(false);
                      setPageInput('');
                    }}
                    className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder={currentPage.toString()}
                  />
                </form>
              ) : (
                <button
                  onClick={() => setShowPageInput(true)}
                  className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                >
                  Jump to page
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Pagination controls */}
      <div className="flex items-center gap-1">
        {/* First page */}
        {showFirstLast && currentPage > 3 && (
          <>
            <button
              onClick={onFirstPage}
              disabled={!hasPrevPage}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="First page"
            >
              <ChevronsLeft size={16} />
            </button>
            {visiblePages[0] > 2 && (
              <span className="px-2 text-gray-400">
                <MoreHorizontal size={16} />
              </span>
            )}
          </>
        )}

        {/* Previous page */}
        <button
          onClick={onPrevPage}
          disabled={!hasPrevPage}
          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Previous page"
        >
          <ChevronLeft size={16} />
        </button>

        {/* Page numbers */}
        <div className="flex items-center gap-1">
          {visiblePages.map((page) => (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`min-w-[40px] h-10 px-3 text-sm font-medium rounded-md transition-colors ${
                page === currentPage
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
        </div>

        {/* Next page */}
        <button
          onClick={onNextPage}
          disabled={!hasNextPage}
          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Next page"
        >
          <ChevronRight size={16} />
        </button>

        {/* Last page */}
        {showFirstLast && currentPage < totalPages - 2 && (
          <>
            {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
              <span className="px-2 text-gray-400">
                <MoreHorizontal size={16} />
              </span>
            )}
            <button
              onClick={onLastPage}
              disabled={!hasNextPage}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Last page"
            >
              <ChevronsRight size={16} />
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default Pagination;