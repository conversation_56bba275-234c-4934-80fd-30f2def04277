import { faker } from "@faker-js/faker";
import { 
    Button, 
    Col, 
    Form, 
    Input, 
    Row, 
    Space, 
    Switch, 
    Typography, 
    Spin, 
    Timeline, 
    Empty,
    Badge 
} from "antd";
import dayjs from 'dayjs';
import Card from "@/components/card";
import { UploadAvatar } from "@/components/upload";
import { useUserInfo } from "@/store/userStore";
import { toast } from "sonner";
import { Iconify } from "@/components/icon";
import React, { useState, useEffect } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { useParams } from "@/router/hooks";
import transformerService from "@/api/services/transformerService";
import { TransformerData } from "#/entity";
import { themeVars } from "@/theme/theme.css";
import InspectionPage from "./inspection";
import BasestationMap from "./BasestationMap";
import { formatDate } from "@fullcalendar/core/index.js";
import logService from "@/api/services/logService";
import { Link } from "react-router";

type FieldType = {
	name?: string;
	email?: string;
	phone?: string;
	full_name?: string;
	region?: string;
	csc?: string;
	title?: string;
	address?: string;
	city?: string;
	code?: string;
	about: string;
};

// Add proper type for change logs
interface ChangeLog {
	timestamp: string;
	changed_by: string;
	field_name: string;
	old_value: string;
	new_value: string;
}

interface ChangeLogResponse {
	count: number;
	results: ChangeLog[];
}

export default function GeneralTab() {
	const { id } = useParams();
	const [data, setData] = useState<TransformerData[]>([]);
	const [loading, setLoading] = useState(false);
	const [showTimeline, setShowTimeline] = useState(false);
	const [changeLogs, setChangeLogs] = useState<ChangeLog[]>([]);
	const [timelineLoading, setTimelineLoading] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const ITEMS_PER_PAGE = 10;

	const fetchChangeLogs = async (page: number = 1, append: boolean = false) => {
		if (!id) return;
		
		setTimelineLoading(true);
		try {
			const response = await logService.getSpecificChangeLogs(
				id,
				'Transformer Data',
				{
					page,
					pageSize: ITEMS_PER_PAGE,
					field_name: "basestation",
				}
			);

			const newLogs = response?.results || [];
			const totalCount = response?.count || 0;

			if (append) {
				setChangeLogs(prev => [...prev, ...newLogs]);
			} else {
				setChangeLogs(newLogs);
			}

			// Check if we have more items to load
			setHasMore(currentPage * ITEMS_PER_PAGE < totalCount);
		} catch (error) {
			console.error("Error fetching change logs:", error);
			toast.error("Failed to load change logs");
			setChangeLogs([]);
		} finally {
			setTimelineLoading(false);
		}
	};

	const handleLoadMore = () => {
		const nextPage = currentPage + 1;
		setCurrentPage(nextPage);
		fetchChangeLogs(nextPage, true);
	};

	const handleTimelineToggle = () => {
		if (!showTimeline && changeLogs.length === 0) {
			setCurrentPage(1);
			fetchChangeLogs(1, false);
		}
		setShowTimeline(!showTimeline);
	};

	// Fetch data when tableParams change
	useEffect(() => {
		const fetchData = async () => {
			setLoading(true);
			try {
				const response = await transformerService.getPopulatedTransformer(id); // Fetch data from the service
				setData(response);
			} catch (error) {
				console.error("Error fetching transformer data:", error);
				toast.error("Failed to load transformer data.");
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, []);

	const AboutTransformer = [
		{
			label: "Transformer ID",
			val: data?.id,
		},
		{
			label: "Station Code",
			val: <Link to={`/basestation/${data?.basestation?.station_code}`}>{data?.basestation?.station_code}</Link>,
		},

		{
			label: "Transformer Type",
			val: data?.trafo_type,
		},
		{
			label: "Capacity",
			val: data?.capacity,
		},
		{
			label: "DT Number",
			val: data?.dt_number,
		},
		{
			label: "Primary Voltage",
			val: data?.primary_voltage,
		},
		{
			label: "Cooling Type",
			val: data?.colling_type,
		},
		{
			label: "Serial Number",
			val: data?.serial_number,
		},
		{
			label: "Manufacturer",
			val: data?.manufacturer,
		},
		{
			label: "Vector Group",
			val: data?.vector_group,
		},
		{
			label: "Impedance Voltage",
			val: data?.impedance_voltage,
		},
		{
			label: "Winding Weight",
			val: data?.winding_weight,
		},
		{
			label: "Oil Weight",
			val: data?.oil_weight,
		},
		{
			label: "Year of Manufacturing",
			val: data?.year_of_manufacturing,
		},
		{
			label: "Date of Installation",
			val: data?.date_of_installation,
		},
		{
			label: "Created_by",
			val: data?.created_by?.username,
		},
		{
			label: "Updated_by",
			val: data?.updated_by?.username,
		},
		{
			label: "Created_at",
			val: formatDate(data?.created_at),
		},
		{
			label: "Updated_at",
			val: formatDate(data?.updated_at),
		},
	];

	const AboutBase_Station = [
		{
			label: "Station Code",
			val: data?.basestation?.station_code,
		},
		{
			label: "Region",
			val: data?.basestation?.region,
		},
		{
			label: "CSC",
			val: data?.basestation?.csc,
		},

		{
			label: "Substation",
			val: data?.basestation?.substation,
		},
		{
			label: "Feeder",
			val: data?.basestation?.feeder,
		},
		{
			label: "Address",
			val: data?.basestation?.address,
		},
		{
			label: "GPS Location",
			val: data?.basestation?.gps_location,
		},

		{
			label: "Created_by",
			val: data?.basestation?.created_by?.username,
		},
		{
			label: "Updated_by",
			val: data?.basestation?.updated_by?.username,
		},
		{
			label: "Created_at",
			val: formatDate(data?.basestation?.created_at),
		},
		{
			label: "Updated_at",
			val: formatDate(data?.basestation?.updated_at),
		},
	];

	return (
		<div>
			<Row gutter={[16, 16]}>
				<Col span={24} lg={12}>
					<Card className="flex-col">
						<div className="flex w-full flex-col">
							<Typography.Title level={2}>Transformer Details</Typography.Title>
							{/* <Typography.Text>{faker.lorem.paragraph()}</Typography.Text> */}

							<div className="mt-2 flex flex-col gap-4">
								{AboutTransformer.map((item) => (
									<div className="flex" key={item.label}>
										{/* <div className="mr-2">{item.icon}</div> */}
										<div className="mr-2">{item.label}:</div>
										<div className="opacity-50">{item.val}</div>
									</div>
								))}
							</div>
						</div>
					</Card>
				</Col>
				<Col span={24} xs={24} sm={24} md={24} lg={12}>
					<div className="flex justify-end mb-4">
						<Button 
							type="text"
							icon={<Iconify icon="material-symbols-light:move-outline-rounded" />}
							onClick={handleTimelineToggle}
						>
						 Movement History
						</Button>
					</div>
					<div className="flex items-center justify-center h-auto">
						{showTimeline ? (
							<Card className="w-full flex-col">
								<Typography.Title level={4}>Transformer Movement History</Typography.Title>

								{changeLogs.length > 0 && (
									<div className="bg-gray-50 p-4 rounded-lg mb-4">
										<p className="font-medium text-primary mb-2">Movement Base Station Path:</p>
										<div className="flex items-center flex-wrap gap-2">
											{[...changeLogs].reverse().map((log, index, array) => (
												<React.Fragment key={index}>
													{index === 0 ? (
														<span className="text-gray-600">{log.old_value || "none"}</span>
													) : null}
													<Iconify 
														icon="material-symbols:arrow-forward" 
														className="text-primary"
													/>
														<span className="text-gray-800">{log.new_value || "none"}</span>
												</React.Fragment>
											))}
										</div>
									</div>
								)}
								<Timeline>
									{changeLogs.map((log, index) => (
										<Timeline.Item 
											key={index}
											dot={
												<Badge 
													status="processing" 
													style={{ backgroundColor: themeVars.colors.primary }}
												/>
											}
										>
											<div className="bg-gray-50 p-2 rounded-lg mb-2">
												<p className="font-medium text-primary">
													{dayjs(log.timestamp).format("MMM DD, YYYY h:mm A")}
												</p>
												<p className="text-gray-600">Changed by: {log.changed_by}</p>
												<p className="text-gray-600">Field: {log.field_name}</p>
												<p className="text-gray-600">Reason: {log.reason}</p>
												<div className="mt-2">
													<p className="text-gray-500 line-through">From: {log.old_value}</p>
													<p className="text-gray-800">To: {log.new_value}</p>
												</div>
											</div>
										</Timeline.Item>
									))}
								</Timeline>

								

								{changeLogs.length === 0 && !timelineLoading && (
									<Empty description="No Movement found" />
									// <Empty description="No change logs found" />
								)}
								
								{timelineLoading && (
									<div className="flex justify-center p-4">
										<Spin />
									</div>
								)}
								
								{!timelineLoading && hasMore && changeLogs.length > 0 && (
									<div className="flex justify-center mt-4">
										<Button 
											onClick={handleLoadMore}
											type="default"
											icon={<Iconify icon="mdi:chevron-down" />}
										>
											Load More
										</Button>
									</div>
								)}
							</Card>
						) : (
							<TransformerSvg />
						)}
					</div>
				</Col>

				<Col span={24} lg={12}>
					{data?.basestation?.gps_location && <BasestationMap gps_location={data?.basestation?.gps_location} />}
				</Col>
				<Col span={24} lg={12}>
					{data?.basestation ? (
						<Card className="flex-col">
							<div className="flex w-full flex-col">
								<Typography.Title level={2}>Base Station Details</Typography.Title>
								<div className="mt-2 flex flex-col gap-4">
									{AboutBase_Station.map((item) => (
										<div className="flex" key={item.label}>
											<div className="mr-2">{item.label}:</div>
											<div className="opacity-50">{item.val}</div>
										</div>
									))}
								</div>
							</div>
						</Card>
					) : (
						<Card className="flex-col">
							<Empty description="No Base Station found" />
						</Card>
					)}
				</Col>
			</Row>

			<div className="mt-10 mx-2">
				<InspectionPage id={id} />
			</div>
		</div>
	);
}

function TransformerSvg() {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			xmlnsXlink="http://www.w3.org/1999/xlink"
			width={312}
			height={312}
			// x={0}
			// y={0}
			viewBox="0 0 53 53"
			// style={{
			//   enableBackground: "new 0 0 512 512",
			// }}
			// xmlSpace="preserve"
			className=""
			// {...props}
		>
			<g>
				<path
					fill="#f8f9fe"
					d="m44.76 36.382-.01 1.62 1.3.74-.01.62-19.15 11.14-3.13-1.81v-.62l1.3-.76.01-1.6-1.3-.75v-.62l1.09-.63-9.83-5.67-4.79 2.78-3.13-1.81v-.62l1.29-.75.01-1.61-1.29-.75v-.62l1.02-.6-1.2-.69.07-23.03 13.49-7.84 1.92 1.11 2.75-.76c.12-.***********.15.03.12-.04.24-.15.27l-2.33.65 4.51 2.6 2.47-1.44 14.56 8.4-.05 14.71-1.03.6-.01 4.7 2.65 1.52-.01.62z"
					opacity={1}
					data-original="#f8f9fe"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-f8f9fe, #1a1c1e)",
					}}
					className=""
				/>
				<path
					fill="#9ea9c9"
					d="m40.453 15.833 4.043-2.35-.041 14.706-4.043 2.35z"
					opacity={1}
					data-original="#9ea9c9"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-9ea9c9, #333d5b)",
					}}
					className=""
				/>
				<path
					fill="#f8f9fe"
					d="m25.901 7.431 4.043-2.35 14.552 8.402-4.043 2.35z"
					opacity={1}
					data-original="#f8f9fe"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-f8f9fe, #1a1c1e)",
					}}
					className=""
				/>
				<path
					fill="#b0b8cf"
					d="m40.453 15.833-.041 14.705-14.553-8.401.042-14.706z"
					opacity={1}
					data-original="#b0b8cf"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-b0b8cf, #30384f)",
					}}
					className=""
				/>
				<path
					fill="#e3e7f0"
					d="m39.697 16.269-.038 12.964-1.318-.76-1.25-.722-1.8-1.042-1.25-.722-1.802-1.042-1.25-.722-1.8-1.036-1.25-.722-1.325-.766.038-12.963 1.325.766 1.243.715 1.801 1.042 1.25.722 1.808 1.042 1.243.722 1.801 1.036 1.25.722z"
					opacity={1}
					data-original="#e3e7f0"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-e3e7f0, #242729)",
					}}
					className=""
				/>
				<path
					fill="#b0b8cf"
					d="m38.372 15.503-.03 12.97-1.25-.722.031-12.97zM35.322 13.745 35.29 26.71l-1.25-.722.039-12.964zM32.27 11.981l-.03 12.964-1.25-.722.031-12.964zM29.22 10.217l-.032 12.97-1.25-.722.039-12.963z"
					opacity={1}
					data-original="#b0b8cf"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-b0b8cf, #30384f)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="m44.76 36.382-.01 1.62 1.3.74-.01.62-19.15 11.14-3.13-1.81v-.62l1.3-.76.01-1.6-1.3-.75v-.62l19.15-11.13 3.14 1.8-.01.62zM29.39 29.692l-19.15 11.13-3.13-1.81v-.62l1.29-.75.01-1.61-1.29-.75v-.62l19.15-11.13 3.13 1.81v.62l-1.3.76v1.6l1.29.75z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<path
					fill="#4c5c75"
					d="m23.762 48.07 19.152-11.13 1.296.747-19.152 11.13zM25.597 49.13 44.75 37.998l1.296.748-19.153 11.13z"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-4c5c75, #3d4a5e)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="m26.893 49.877 19.153-11.13-.002.62-19.153 11.13zM25.606 46.02l19.153-11.131-.01 3.11-19.152 11.13z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="m26.904 46.147 19.152-11.13-.001.62-19.153 11.13z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<path
					fill="#4c5c75"
					d="m23.772 44.34 19.153-11.131 3.131 1.808-19.152 11.13z"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-4c5c75, #3d4a5e)",
					}}
					className=""
				/>
				<path
					fill="#111d33"
					d="m26.904 46.147-.002.62-1.296-.748-.009 3.11 1.296.748-.002.62-3.131-1.808.002-.62 1.296.749.008-3.11-1.296-.749.002-.62z"
					opacity={1}
					data-original="#111d33"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-111d33, #0e1729)",
					}}
				/>
				<path
					fill="#4c5c75"
					d="m7.107 38.39 19.152-11.13 1.297.749-19.153 11.13zM8.942 39.45l19.153-11.13 1.296.748-19.152 11.13z"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-4c5c75, #3d4a5e)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="m10.239 40.199 19.152-11.13-.002.62-19.152 11.13zM8.951 36.34l19.153-11.13-.009 3.11L8.942 39.45z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="m10.25 36.469 19.152-11.13-.002.62-19.153 11.13z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<path
					fill="#4c5c75"
					d="M7.117 34.66 26.27 23.53l3.132 1.808-19.153 11.13z"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-4c5c75, #3d4a5e)",
					}}
					className=""
				/>
				<path
					fill="#111d33"
					d="m10.25 36.469-.003.62-1.296-.749-.009 3.11 1.297.749-.002.62-3.132-1.808.002-.62 1.296.748.009-3.11-1.296-.748.001-.62z"
					opacity={1}
					data-original="#111d33"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-111d33, #0e1729)",
					}}
				/>
				<path
					fill="#e3e7f0"
					d="M43.462 15.756c-4.102 5.515-8.559 8.443-13.497 7.841-4.882-.337-14.04-6.97-22.958-13.252l13.491-7.847z"
					opacity={1}
					data-original="#e3e7f0"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-e3e7f0, #242729)",
					}}
					className=""
				/>
				<path
					fill="#c9cee2"
					d="M29.965 23.598c1.766 5.21.98 13.94-.066 23.029L6.941 33.37l.065-23.028z"
					opacity={1}
					data-original="#c9cee2"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-c9cee2, #2f3335)",
					}}
					className=""
				/>
				<path
					fill="#b0b8cf"
					d="m43.46 15.756-.065 23.028-13.496 7.843.066-23.029z"
					opacity={1}
					data-original="#b0b8cf"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-b0b8cf, #30384f)",
					}}
					className=""
				/>
				<path
					fill="#c9cee2"
					d="m9.512 10.34.859.496L20.5 4.944V3.95zM20.5 3.949v.995L40.099 16.26l.86-.5z"
					opacity={1}
					data-original="#c9cee2"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-c9cee2, #2f3335)",
					}}
					className=""
				/>
				<path
					fill="#b0b8cf"
					d="M40.098 16.259 20.5 4.944l-10.13 5.892 19.592 11.31z"
					opacity={1}
					data-original="#b0b8cf"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-b0b8cf, #30384f)",
					}}
					className=""
				/>
				<path
					fill="#9ea9c9"
					d="m23.238 26.746 4.043-2.35-.041 14.705-4.043 2.35z"
					opacity={1}
					data-original="#9ea9c9"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-9ea9c9, #333d5b)",
					}}
					className=""
				/>
				<path
					fill="#f8f9fe"
					d="m8.686 18.344 4.043-2.35 14.552 8.402-4.043 2.35z"
					opacity={1}
					data-original="#f8f9fe"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-f8f9fe, #1a1c1e)",
					}}
					className=""
				/>
				<path
					fill="#b0b8cf"
					d="m23.238 26.746-.041 14.705-14.553-8.402.042-14.705z"
					opacity={1}
					data-original="#b0b8cf"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-b0b8cf, #30384f)",
					}}
					className=""
				/>
				<path
					fill="#e3e7f0"
					d="m22.482 27.182-.038 12.963-1.318-.76-1.25-.721-1.8-1.042-1.25-.722-1.802-1.042-1.25-.722-1.8-1.036-1.25-.722-1.325-.766.038-12.964 1.325.766 1.243.716 1.801 1.042 1.25.722 1.808 1.042 1.243.722 1.801 1.036 1.25.722z"
					opacity={1}
					data-original="#e3e7f0"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-e3e7f0, #242729)",
					}}
					className=""
				/>
				<path
					fill="#c9cee2"
					d="M35.612 28.312 32.5 26.503l.032 11.32 3.113 1.81z"
					opacity={1}
					data-original="#c9cee2"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-c9cee2, #2f3335)",
					}}
					className=""
				/>
				<path
					fill="#f8f9fe"
					d="m42.689 24.226-3.113-1.808-7.076 4.085 3.112 1.809z"
					opacity={1}
					data-original="#f8f9fe"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-f8f9fe, #1a1c1e)",
					}}
					className=""
				/>
				<path
					fill="#9ea9c9"
					d="m35.612 28.312.033 11.32 7.076-4.085-.032-11.32z"
					opacity={1}
					data-original="#9ea9c9"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-9ea9c9, #333d5b)",
					}}
					className=""
				/>
				<path
					fill="#e3e7f0"
					d="m42.14 35.212-.029-9.983-5.917 3.417.028 9.983z"
					opacity={1}
					data-original="#e3e7f0"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-e3e7f0, #242729)",
					}}
					className=""
				/>
				<path
					fill="#b0b8cf"
					d="m21.158 26.416-.032 12.97-1.25-.722.032-12.97zM18.107 24.658l-.032 12.964-1.25-.722.039-12.964zM15.056 22.894l-.032 12.964-1.25-.722.032-12.964zM12.005 21.13l-.032 12.97-1.25-.722.039-12.964zM36.855 28.264l.014 10.004.554-.32-.014-10.004zM38.207 27.485l.014 10.002.553-.32-.016-10.001zM39.559 26.704l.014 10.001.553-.32-.014-10zM40.91 25.922l.014 10.004.554-.32-.017-10z"
					opacity={1}
					data-original="#b0b8cf"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-b0b8cf, #30384f)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M32.45 7.052c.03.12-.03.24-.15.27l-3.55.99h-.03l.46.88c.04.01.08.02.11.04.28.16.36.39.26.6.03-.05.06-.1.06-.16l-.01 2.35c0 .16-.11.32-.32.44-.42.25-1.11.25-1.53 0-.21-.12-.32-.28-.32-.44l.01-2.35c0-.16.1-.32.31-.44.03-.02.08-.03.11-.04l.66-1.26.02.03c.02-.03.05-.07.09-.08l3.55-.98c.12-.***********.15zM20.42 7.972v-2.35h.01c0-.16.1-.32.31-.44.03-.02.07-.03.11-.04l.66-1.26.01.03c.03-.03.05-.06.09-.08l3.56-.98c.12-.***********.15.03.12-.04.24-.15.27l-3.56.99h-.02l.46.88c.03.01.07.02.1.04.24.14.34.33.3.52.01-.02.02-.04.02-.07l-.01 2.34c0 .16-.1.32-.31.44-.42.25-1.11.25-1.53 0-.22-.12-.32-.28-.32-.44zM22.91 13.692c.03-.05.05-.1.05-.15v2.35c-.01.15-.11.31-.32.44-.42.24-1.11.24-1.53 0-.22-.13-.32-.29-.32-.45l.01-2.35c0-.16.1-.32.31-.44.03-.02.08-.03.11-.04l.32-.6-3.21 3.28c-.04.04-.1.06-.16.06a.22.22 0 0 1-.15-.06c-.09-.08-.09-.22-.01-.31l3.56-3.63c.08-.09.22-.09.31 0l.66 1.26c.04.02.07.02.1.04.29.16.37.39.27.6zM15.93 9.562c.01-.02.02-.04.02-.07l-.01 2.35c0 .16-.1.32-.31.44-.42.24-1.11.24-1.54 0-.21-.13-.32-.29-.32-.45l.01-2.35c0-.16.11-.32.32-.44.03-.02.07-.02.11-.04l.31-.6-3.2 3.28c-.05.04-.1.07-.16.07s-.11-.02-.16-.07c-.08-.08-.08-.22 0-.31l3.55-3.63c.09-.09.23-.09.31 0 .01 0 .01 0 .01.01l.66 1.25c.03.02.07.02.1.04.24.14.34.33.3.52zM29.96 17.662c.01-.02.02-.04.02-.07l-.01 2.34c0 .16-.11.32-.32.44-.42.25-1.1.25-1.53 0-.21-.12-.32-.28-.32-.44l.01-2.35c0-.16.11-.32.32-.44.03-.02.07-.03.1-.04l.32-.6-3.21 3.28c-.04.04-.1.06-.16.06a.22.22 0 0 1-.15-.06c-.09-.08-.09-.22 0-.31l3.55-3.63c.08-.09.22-.09.31-.01v.01h.01l.66 1.26c.03.01.07.02.1.04.24.14.34.33.3.52zM39.46 11.102c.04.12-.03.24-.15.27l-3.55.98c-.01.01-.02.01-.03.01l.47.88c.03.01.07.02.1.04.24.14.34.32.3.51.01-.03.02-.05.02-.07l-.01 2.35c0 .16-.11.32-.32.44-.42.25-1.1.25-1.53 0-.21-.12-.32-.28-.32-.45l.01-2.34c0-.16.11-.32.32-.44.03-.02.07-.03.1-.05l.67-1.25.01.02c.03-.03.05-.06.09-.07l3.55-.98c.12-.***********.15z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="m29.978 17.588-.01 2.348c0 .16-.105.319-.314.44-.422.245-1.108.245-1.532 0-.214-.123-.321-.285-.32-.446l.009-2.349c0 .162.106.324.32.447.424.245 1.11.245 1.532 0 .21-.122.314-.281.315-.44z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
				<ellipse
					cx={28.894}
					cy={17.584}
					fill="#4c5c75"
					rx={0.628}
					ry={1.083}
					transform="rotate(-89.83 28.898 17.584)"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-4c5c75, #aba499)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M29.496 17.877c-.33.193-.868.193-1.201 0-.217-.124-.294-.302-.228-.46h-.002l.016-.03.815-1.542.788 1.5c.122.177.063.389-.188.532z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M25.185 19.846a.22.22 0 0 1-.157-.373l3.552-3.632a.22.22 0 1 1 .314.307l-3.552 3.631a.218.218 0 0 1-.157.067zM22.965 13.54l-.01 2.348c0 .159-.105.318-.315.44-.42.245-1.108.245-1.532 0-.214-.123-.32-.285-.32-.447l.01-2.348c-.001.161.105.323.32.447.423.244 1.11.244 1.531 0 .21-.122.315-.282.316-.44z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<ellipse
					cx={21.881}
					cy={13.536}
					fill="#4c5c75"
					rx={0.628}
					ry={1.083}
					transform="rotate(-89.83 21.884 13.535)"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-4c5c75, #aba499)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M22.483 13.828c-.33.194-.868.194-1.201 0-.217-.124-.294-.301-.228-.46h-.002l.015-.029.815-1.542.789 1.5c.121.177.063.389-.188.531z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M18.171 15.797a.22.22 0 0 1-.157-.373l3.553-3.632a.22.22 0 1 1 .314.307l-3.553 3.632a.218.218 0 0 1-.157.066zM15.951 9.49l-.009 2.35c0 .158-.105.318-.315.44-.421.244-1.108.244-1.532 0-.214-.124-.32-.286-.32-.447l.01-2.349c-.001.162.105.324.32.447.423.245 1.11.245 1.531 0 .21-.122.315-.281.315-.44z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<ellipse
					cx={14.868}
					cy={9.487}
					fill="#4c5c75"
					rx={0.628}
					ry={1.083}
					transform="rotate(-89.83 14.87 9.487)"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-4c5c75, #aba499)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M15.47 9.78c-.331.193-.868.193-1.202 0-.217-.124-.293-.302-.227-.46h-.003l.016-.03.815-1.542.788 1.5c.122.177.064.389-.188.532z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M11.158 11.749a.22.22 0 0 1-.157-.373l3.553-3.632a.22.22 0 1 1 .314.307l-3.553 3.632a.218.218 0 0 1-.157.066zM36.619 13.725l-.01 2.348c0 .16-.105.319-.315.441-.42.245-1.108.245-1.532 0-.214-.124-.32-.285-.32-.447l.01-2.348c-.001.161.106.323.32.446.423.245 1.11.245 1.532 0 .21-.121.314-.28.315-.44z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<ellipse
					cx={35.535}
					cy={13.722}
					fill="#4c5c75"
					rx={0.628}
					ry={1.083}
					transform="rotate(-89.83 35.54 13.72)"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-4c5c75, #aba499)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M36.137 14.014c-.33.193-.868.193-1.201 0-.217-.124-.294-.301-.228-.46h-.002l.015-.03.815-1.541.789 1.5c.121.177.063.388-.188.531z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M35.7 12.365a.22.22 0 0 1-.059-.431l3.553-.986a.222.222 0 0 1 .************ 0 0 1-.152.27l-3.553.986a.23.23 0 0 1-.059.008zM29.605 9.676l-.009 2.349c0 .16-.105.319-.315.44-.421.245-1.108.245-1.532 0-.214-.123-.32-.285-.32-.446l.01-2.349c-.001.162.105.323.32.447.423.245 1.11.245 1.531 0 .21-.122.315-.281.315-.44z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<ellipse
					cx={28.522}
					cy={9.673}
					fill="#4c5c75"
					rx={0.628}
					ry={1.083}
					transform="rotate(-89.83 28.525 9.671)"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-4c5c75, #aba499)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M29.124 9.966c-.331.193-.868.193-1.202 0-.216-.125-.293-.302-.227-.46h-.003l.016-.03.815-1.542.788 1.5c.122.177.064.389-.187.532z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M28.687 8.317a.22.22 0 0 1-.059-.432l3.553-.986a.22.22 0 0 1 .118.424l-3.553.986a.23.23 0 0 1-.059.008zM22.592 5.628l-.01 2.349c0 .159-.104.318-.314.44-.421.245-1.108.245-1.532 0-.214-.123-.32-.285-.32-.447l.009-2.348c0 .161.106.323.32.447.424.244 1.11.244 1.532 0 .21-.122.315-.282.315-.441z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-background-1d2943, #172136)",
					}}
					className=""
				/>
				<ellipse
					cx={21.508}
					cy={5.625}
					fill="#4c5c75"
					rx={0.628}
					ry={1.083}
					transform="rotate(-89.83 21.51 5.623)"
					opacity={1}
					data-original="#4c5c75"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-4c5c75, #aba499)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M22.11 5.917c-.33.193-.868.193-1.2 0-.218-.124-.295-.301-.228-.46h-.003l.016-.03.815-1.541.788 1.5c.122.177.064.388-.188.531z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
				<path
					fill="#1d2943"
					d="M21.674 4.269a.22.22 0 0 1-.06-.432l3.553-.986a.22.22 0 0 1 .118.424l-3.553.986a.23.23 0 0 1-.058.008z"
					opacity={1}
					data-original="#1d2943"
					data-darkreader-inline-fill=""
					style={{
						"--darkreader-inline-fill": "var(--darkreader-text-1d2943, #cac5be)",
					}}
					className=""
				/>
			</g>
		</svg>
	);
}














