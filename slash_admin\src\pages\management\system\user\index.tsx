import { <PERSON><PERSON>, <PERSON>, Popconfirm, Tag } from "antd";
import Table, { type ColumnsType } from "antd/es/table";

import { USER_LIST } from "@/_mock/assets";
import { IconButton, Iconify } from "@/components/icon";
import { usePathname, useRouter } from "@/router/hooks";
import { getFullAvatarUrl } from "@/utils/url";

import type { UserInfo, Role } from "#/entity";
import { BasicStatus } from "#/enum";
import { toast } from "sonner";
import roleService from "@/api/services/roleService";
import { useEffect, useState } from "react";
import { UserModal, UserModalProps } from "./user-modal";

// const USERS: UserInfo[] = USER_LIST as UserInfo[];

const DEFAULT_USER_VALUE: UserInfo = {
	id: "",
	username: "",
	email: "",
	is_active: true,
	avatar: "",
	// role: [],
	// permission: [],
};

export default function RolePage() {
	const { push } = useRouter();
	const pathname = usePathname();

	const [data, setData] = useState<any[]>([]);
	const [loading, setLoading] = useState(false);

	const [userModalProps, setUserModalProps] = useState<UserModalProps>({
		formValue: { ...DEFAULT_USER_VALUE },
		title: "New",
		show: false,
		onOk: () => {
			setUserModalProps((prev) => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setUserModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const fetchData = async () => {
		setLoading(true);
		try {
			const response = await roleService.getUser(); // Fetch data from the service
			console.log("API Response role:", response);

			if (response) {
				setData(response);
			}
		} catch (error) {
			console.error("Error fetching Role:", error);
			toast.error("Failed to load Role.");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchData();
	}, []);

	const columns: ColumnsType<UserInfo> = [
		{
			title: "username",
			dataIndex: "username",
			width: 300,
			render: (_, record) => {
				return (
					<div className="flex">
						<img alt="" src={getFullAvatarUrl(record.avatar)} className="h-10 w-10 rounded-full" />
						<div className="ml-2 flex flex-col">
							<span className="text-sm">{record.username}</span>
							<span className="text-xs text-text-secondary">{record.full_name || record.email}</span>
						</div>
					</div>
				);
			},
		},
		{
			title: "Role",
			dataIndex: "role",
			align: "center",
			width: 120,
			render: (role: Role) => <Tag color="cyan">{role?.name}</Tag>,
		},
		{
			title: "Status",
			dataIndex: "is_active",
			align: "center",
			width: 120,
			render: (is_active) => <Tag color={is_active ? "success" : "error"}>{is_active ? "Enable" : "Disable"}</Tag>,
		},
		{
			title: "Action",
			key: "operation",
			align: "center",
			width: 100,
			// render: (_, record) => (
			// 	<div className="flex w-full justify-center text-gray-500">
			// 		<IconButton
			// 			onClick={() => {
			// 				push(`${pathname}/${record.id}`);
			// 			}}
			// 		>
			// 			<Iconify icon="mdi:card-account-details" size={18} />
			// 		</IconButton>
			// 		<IconButton onClick={() => onEdit(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 		<Popconfirm
			// 			title="Delete the User"
			// 			okText="Yes"
			// 			cancelText="No"
			// 			placement="left"
			// 			onConfirm={() => handleDelete(record.id)}
			// 		>
			// 			<IconButton>
			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
			// 			</IconButton>
			// 		</Popconfirm>
			// 	</div>
			// ),

			render: (_, record) => (
							<div className="flex w-full justify-center text-gray gap-2">
								<IconButton onClick={() => { push(`${pathname}/${record.id}`);}}>
									<Iconify icon="ix:view" size={18} />
								</IconButton>
								<IconButton onClick={() => onEdit(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
									<Popconfirm
										title="Delete the User"
										okText="Yes"
										cancelText="No"
										placement="left"
										onConfirm={() => handleDelete(record.id)}
										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
									>
										<IconButton>
											<Iconify icon="ix:delete" size={18} className="text-error" />
										</IconButton>
									</Popconfirm>
							</div>
						),
		},
	];

	const onCreate = () => {
		setUserModalProps((prev) => ({
			...prev,
			show: true,
			title: "Create User",
			formValue: {
				...prev.formValue,
				...DEFAULT_USER_VALUE,
			},
		}));
	};

	const onEdit = (formValue: UserInfo) => {
		setUserModalProps((prev) => ({
			...prev,
			show: true,
			title: "Edit",
			formValue,
		}));
	};

	const handleDelete = async (id: string) => {
		try {
			await roleService.deleteUser(id); // Call the delete API
			toast.success("User deleted successfully!");
			fetchData(); // Refresh the table data
		} catch (error) {
			toast.error("Failed to delete User.");
		}
	};

	return (
		<Card
			title="User List"
			extra={
				<Button type="primary" onClick={onCreate}>
					New
				</Button>
			}
		>
			<Table
				rowKey="id"
				size="small"
				scroll={{ x: "max-content" }}
				pagination={false}
				columns={columns}
				dataSource={data}
			/>

			<UserModal {...userModalProps} />
		</Card>
	);
}
