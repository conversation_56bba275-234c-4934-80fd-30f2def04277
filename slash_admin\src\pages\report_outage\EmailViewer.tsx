// import React from 'react';
// import { ArrowLeft, Star, Archive, Trash2, Reply, ReplyAll, Forward, MoreHorizontal, Paperclip } from 'lucide-react';
// import { Email } from '../../types/email';

// interface EmailViewerProps {
//   email: Email;
//   onBack: () => void;
//   onToggleStar: (emailId: number) => void;
//   onArchive: (emailId: number) => void;
//   onDelete: (emailId: number) => void;
// }

// const EmailViewer: React.FC<EmailViewerProps> = ({
//   email,
//   onBack,
//   onToggleStar,
//   onArchive,
//   onDelete,
// }) => {

//   console.log('Email Viewer Rendered', email);
//   return (
//     <div className="flex-1 flex flex-col bg-white">
//       {/* Header */}
//       <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
//         <div className="flex items-center gap-4">
//           <button
//             onClick={onBack}
//             className="p-2 hover:bg-gray-100 rounded-full transition-colors"
//           >
//             <ArrowLeft size={20} className="text-gray-600" />
//           </button>
//           <h1 className="text-lg font-medium text-gray-900 truncate">
//             {email.subject}
//           </h1>
//         </div>

//         <div className="flex items-center gap-2">
//           <button
//             onClick={() => onToggleStar(email.id)}
//             className="p-2 hover:bg-gray-100 rounded-full transition-colors"
//           >
//             <Star
//                       size={20}
//                       color={email.starred ? '#facc15' : '#9ca3af'} // yellow-400 or gray-400
//                       fill={email.starred ? '#facc15' : 'none'}
//                       className="transition-colors"
//             />
//           </button>
//           <button
//             onClick={() => onArchive(email.id)}
//             className="p-2 hover:bg-gray-100 rounded-full transition-colors"
//           >
//             <Archive size={20} className="text-gray-600" />
//           </button>
//           <button
//             onClick={() => onDelete(email.id)}
//             className="p-2 hover:bg-gray-100 rounded-full transition-colors"
//           >
//             <Trash2 size={20} className="text-gray-600" />
//           </button>
//           <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
//             <MoreHorizontal size={20} className="text-gray-600" />
//           </button>
//         </div>
//       </div>

//       {/* Email Content */}
//       <div className="flex-1 overflow-y-auto">
//         <div className="max-w-4xl mx-auto p-6">
//           {/* Email Header */}
//           <div className="mb-6">
//             <div className="flex items-start justify-between mb-4">
//               <div className="flex items-center gap-4">
//                 <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
//                   <span className="text-white font-medium text-lg">
//                     {email.sender.charAt(0).toUpperCase()}
//                   </span>
//                 </div>
//                 <div>
//                   <h2 className="text-xl font-semibold text-gray-900 mb-1">
//                     {email.subject}
//                   </h2>
//                   <div className="flex items-center gap-2 text-sm text-gray-600">
//                     <span className="font-medium">{email.sender}</span>
//                     <span>•</span>
//                     <span>{email.timestamp}</span>
//                   </div>
//                 </div>
//               </div>
//             </div>

//             {/* Labels */}
//             {email.labels && email.labels.length > 0 && (
//               <div className="flex gap-2 mb-4">
//                 {email.labels.map((label) => (
//                   <span
//                     key={label}
//                     className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full"
//                   >
//                     {label}
//                   </span>
//                 ))}
//               </div>
//             )}

//             {/* Attachments */}
//             {email.attachments && email.attachments > 0 && (
//               <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg mb-4">
//                 <Paperclip size={16} className="text-gray-500" />
//                 <span className="text-sm text-gray-700">
//                   {email.attachments} attachment{email.attachments > 1 ? 's' : ''}
//                 </span>
//               </div>
//             )}
//           </div>

//           {/* Email Body */}
//           <div className="prose max-w-none">
//             <div className="whitespace-pre-wrap text-gray-900 leading-relaxed">
//               {email.content}
//             </div>
//           </div>

//           {/* Action Buttons */}
//           <div className="flex gap-3 mt-8 pt-6 border-t border-gray-200">
//             <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
//               <Reply size={16} />
//               Reply
//             </button>
//             <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
//               <ReplyAll size={16} />
//               Reply All
//             </button>
//             <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
//               <Forward size={16} />
//               Forward
//             </button>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default EmailViewer;





import React, { useState } from 'react';
import { ArrowLeft, Star, Archive, Trash2, Reply, ReplyAll, Forward, MoreHorizontal, Paperclip, ChevronDown } from 'lucide-react';
import { Email } from '../../types/email';
import { Select } from 'antd';

interface EmailViewerProps {
  email: Email;
  onBack: () => void;
  onToggleStar: (emailId: number) => void;
  onArchive: (emailId: number) => void;
  onDelete: (emailId: number) => void;
  // onStatusChange: (emailId: number, status: string) => void;
  // onSaveRequestNumber: (emailId: number, requestNumber: string) => void;
  // onSaveRemark: (emailId: number, remark: string) => void;
 onSaveAll: (emailId: number, requestNumber: string, remark: string, status: string) => void;
}

const EmailViewer: React.FC<EmailViewerProps> = ({
  email,
  onBack,
  onToggleStar,
  onArchive,
  onDelete,
  onSaveAll,
}) => {
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);

  const [requestNumber, setRequestNumber] = useState(email.requestNumber || '');
  const [remark, setRemark] = useState(email.remark || '');
  const [status, setStatus] = useState(email.status || '');

  // Save all fields to DB
  const handleSaveAll = () => {

    console.log('Saving all fields...');
    onSaveAll(email.id, requestNumber, remark, status);
  };


  function formatEmailDate(dateString: string) {
  const date = new Date(dateString);
  const now = new Date();

  // If today, show time (e.g., 11:28 AM)
  if (
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear()
  ) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  // If this year, show "Jun 30"
  if (date.getFullYear() === now.getFullYear()) {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  }

  // Otherwise, show "6/30/2024"
  return date.toLocaleDateString();
}

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
          <h1 className="text-lg font-medium text-gray-900 truncate">
            {email.compliant}
          </h1>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => onToggleStar(email.id)}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Star
              size={20}
              color={email.starred ? '#facc15' : '#9ca3af'}
              fill={email.starred ? '#facc15' : 'none'}
              className="transition-colors"
            />
          </button>
          <button
            onClick={() => onArchive(email.id)}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Archive size={20} className="text-gray-600" />
          </button>
          <button
  onClick={() => {
    if (window.confirm('Are you sure you want to delete this email?')) {
      onDelete(email.id);
    }
  }}
  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
>
  <Trash2 size={20} className="text-gray-600" />
</button>
          <div className="relative">
            <button
              onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <MoreHorizontal size={20} className="text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Email Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto p-6">
          {/* Email Header */}
          <div className="mb-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-lg">
                    {email.sender.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-1">
                    {email.complaint}
                  </h2>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span className="font-medium">{email.sender}</span>
                    <span>•</span>
                    <span>{formatEmailDate(email.created_at)}</span>
                  </div>
                  {/* <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span className="font-medium">{email.phone}</span>
                    <span>•</span>
                    <span>{email.canumber}</span>
                  </div> */}
                </div>
              </div>
            </div>

            {/* <div className="prose max-w-none">
            <div className="whitespace-pre-wrap text-gray-900 leading-relaxed">
                Name :  {email.sender}
                phone : {email.phone}
                address : {email.address}
                canumber : {email.canumber}
              </div>
          </div> */}


          <div className="bg-gray-50 rounded-lg p-4 mb-6 shadow-sm">
  <dl className="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-2">
    <div>
      <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Name :</span>
      <span className="text-base text-gray-900 ml-2">{email.sender}</span>
    </div>
    <div>
      <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Phone :</span>
      <span className="text-base text-gray-900 ml-2">{email.phone}</span>
    </div>
    <div>
      <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Address :</span>
      <span className="text-base text-gray-900 ml-2">{email.address}</span>
    </div>
    <div>
      <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">CA Number :</span>
      <span className="text-base text-gray-900 ml-2">{email.canumber}</span>
    </div>
  </dl>
</div>

            {/* Labels */}
            {/* {email.labels && email.labels.length > 0 && (
              <div className="flex gap-2 mb-4">
                {email.labels.map((label) => (
                  <span
                    key={label}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full"
                  >
                    {label}
                  </span>
                ))}
              </div>
            )} */}

            {/* Attachments */}
            {/* {email.attachments && email.attachments > 0 && (
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg mb-4">
                <Paperclip size={16} className="text-gray-500" />
                <span className="text-sm text-gray-700">
                  {email.attachments} attachment{email.attachments > 1 ? 's' : ''}
                </span>
              </div>
            )} */}

            
          </div>

          {/* Email Body */}
          <div className="prose max-w-none">
            <div className="whitespace-pre-wrap text-gray-900 leading-relaxed">
              {email.content}
            </div>
          </div>

          {/* Action Buttons */}
          {/* <div className="flex gap-3 mt-8 pt-6 border-t border-gray-200">
            <button className="flex items-center gap-2 px-4 py-2 bg-bborder-t border-gray-200lue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <Reply size={16} />
              Reply
            </button>
            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <ReplyAll size={16} />
              Reply All
            </button>
            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <Forward size={16} />
              Forward
            </button>
          </div> */}

          {/* Status Dropdown and Fields */}
          <div className="mt-8 border-t border-gray-200 pt-6">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Request Number */}
              <div className="flex-1">
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Request Number
                </label>
                <input
                  type="text"
                  value={requestNumber}
                  onChange={(e) => setRequestNumber(e.target.value)}
            
                  placeholder="Enter request number"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
                />
              </div>

              <div className="flex-1">
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Status
                </label>
                <Select
                placeholder="Action"
                value={status}
            onChange={setStatus}
                style={{ width: 300,  marginTop: 4 }}
                allowClear
                options={[
                    { value: 'Received', label: 'Received' },
                    { value: 'Investigating', label: 'Investigating' },
                    { value: 'Repairing', label: 'Repairing' },
                    { value: 'Restored', label: 'Restored' },
                ]}
            />
                     
              </div>
            </div>
            {/* Remark */}
            <div className="mt-6">
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                Remark
              </label>
              <textarea
                value={remark}
                onChange={(e) => setRemark(e.target.value)}
                rows={3}
                placeholder="Add a remark..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
              ></textarea>
            </div>
            <div className="flex justify-end mt-4">
    <button
     onClick={handleSaveAll}
      className="px-6 py-2 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700 transition"
    >
      Save
    </button>
  </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailViewer;