import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const PaginationControls = () => {
  return (
    <div className="flex items-center">
      <span className="text-sm text-gray-500 mr-4" id="pagination-info">1-50 of 1,024</span>
      <button className="p-2 rounded hover:bg-gray-100 mr-1" id="prev-page" disabled>
        <FaChevronLeft className="text-gray-600" />
      </button>
      <button className="p-2 rounded hover:bg-gray-100" id="next-page">
        <FaChevronRight className="text-gray-600" />
      </button>
    </div>
  );
};

export default PaginationControls;